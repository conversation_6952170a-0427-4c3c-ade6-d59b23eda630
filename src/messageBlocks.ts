import { Block, KnownB<PERSON>, <PERSON><PERSON> } from "@slack/types";
import { ReservationState, ReservationTimeSlot } from "./reservationState";
import { formatTimeRange, formatDuration } from "./utils/timeUtils";
import { ROOM_CALENDARS } from "./meetingRooms";

export function createTimeInputPromptBlocks(
  stateId: string,
  timeSlot: ReservationTimeSlot
): KnownBlock[] {
  return [
    {
      type: "section",
      text: {
        type: "mrkdwn",
        text: `${formatTimeRange(timeSlot.start, timeSlot.end)} 사이의 원하는 시작 시간을 입력해주세요.\n(예: 13시, 13시 30분, 13:00, 13:30)`,
      },
    },
    {
      type: "actions",
      elements: [
        {
          type: "button" as const,
          text: {
            type: "plain_text" as const,
            text: "취소",
          },
          value: JSON.stringify({ stateId, action: "cancel" }),
          action_id: "cancel_reservation",
          style: "danger",
        },
      ],
    },
  ];
}

export function createDurationSelectionBlocks(
  stateId: string,
  startTime: string
): KnownBlock[] {
  return [
    {
      type: "section",
      text: {
        type: "mrkdwn",
        text: `시작 시간 \`${startTime}\`에서 얼마나 예약하시겠습니까?`,
      },
    },
    {
      type: "actions",
      elements: [
        {
          type: "button",
          text: {
            type: "plain_text",
            text: "1시간",
            emoji: true,
          },
          value: JSON.stringify({
            stateId,
            action: "select_duration",
            duration: 60,
          }),
          action_id: "select_duration_60",
        },
        {
          type: "button",
          text: {
            type: "plain_text",
            text: "2시간",
            emoji: true,
          },
          value: JSON.stringify({
            stateId,
            action: "select_duration",
            duration: 120,
          }),
          action_id: "select_duration_120",
        },
        {
          type: "button",
          text: {
            type: "plain_text",
            text: "직접 입력",
            emoji: true,
          },
          value: JSON.stringify({ stateId, action: "custom_duration" }),
          action_id: "custom_duration",
        },
      ],
    },
  ];
}

export function createConfirmationBlocks(
  stateId: string,
  state: ReservationState
): KnownBlock[] {
  if (!state.selectedStartTime || !state.selectedDuration) {
    throw new Error("시작 시간과 예약 시간이 선택되지 않았습니다.");
  }

  const endTime = addMinutesToTime(
    state.selectedStartTime,
    state.selectedDuration
  );

  return [
    {
      type: "section",
      text: {
        type: "mrkdwn",
        text: `*${state.roomName}* 회의실을 다음과 같이 예약하시겠습니까?\n\n시작: ${state.selectedStartTime}\n종료: ${endTime}\n시간: ${formatDuration(state.selectedDuration)}`,
      },
    },
    {
      type: "actions",
      elements: [
        {
          type: "button" as const,
          text: {
            type: "plain_text" as const,
            text: "예약하기",
          },
          value: JSON.stringify({ stateId, action: "confirm" }),
          action_id: "confirm_reservation",
          style: "primary",
        },
        {
          type: "button" as const,
          text: {
            type: "plain_text" as const,
            text: "취소",
          },
          value: JSON.stringify({ stateId, action: "cancel" }),
          action_id: "cancel_reservation",
          style: "danger",
        },
      ],
    },
  ];
}

function addMinutesToTime(time: string, minutes: number): string {
  const [hours, mins] = time.split(":").map(Number);
  const totalMinutes = hours * 60 + mins + minutes;
  const newHours = Math.floor(totalMinutes / 60);
  const newMinutes = totalMinutes % 60;
  return `${newHours.toString().padStart(2, "0")}:${newMinutes.toString().padStart(2, "0")}`;
}
