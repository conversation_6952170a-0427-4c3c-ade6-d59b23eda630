export interface ReservationTimeSlot {
  start: string;
  end: string;
}

export interface ReservationState {
  userId: string;
  channelId: string;
  roomName: string;
  date: string;
  availableSlot: ReservationTimeSlot;
  selectedStartTime?: string;
  selectedDuration?: number;
  messageTs?: string;
  threadTs?: string;
  inputState: "TIME" | "DURATION" | "NONE";
}

// 예약 상태를 저장할 Map
const reservationStates = new Map<string, ReservationState>();

// 예약 상태 생성
export function createReservationState(
  userId: string,
  channelId: string,
  roomName: string,
  date: string,
  availableSlot: ReservationTimeSlot
): string {
  const stateId = `${userId}-${Date.now()}`;
  reservationStates.set(stateId, {
    userId,
    channelId,
    roomName,
    date,
    availableSlot,
    inputState: "TIME",
  });
  return stateId;
}

// 예약 상태 조회
export function getReservationState(
  stateId: string
): ReservationState | undefined {
  return reservationStates.get(stateId);
}

// 예약 상태 업데이트
export function updateReservationState(
  stateId: string,
  updates: Partial<ReservationState>
): boolean {
  const currentState = reservationStates.get(stateId);
  if (!currentState) return false;

  reservationStates.set(stateId, { ...currentState, ...updates });
  return true;
}

// 예약 상태 삭제
export function deleteReservationState(stateId: string): boolean {
  return reservationStates.delete(stateId);
}

// 메시지 타임스탬프로 예약 상태 찾기
export function getReservationStateByMessageTs(
  messageTs: string
): [string, ReservationState] | undefined {
  console.log("Searching for state with messageTs:", messageTs);
  console.log("Current states:", Array.from(reservationStates.entries()));

  for (const [stateId, state] of reservationStates.entries()) {
    console.log("Checking state:", { stateId, state });
    if (state.messageTs === messageTs || state.threadTs === messageTs) {
      console.log("Found matching state:", { stateId, state });
      return [stateId, state];
    }
  }
  console.log("No matching state found");
  return undefined;
}

// 채널 ID로 예약 상태 찾기
export function getReservationStateByChannel(
  channelId: string
): [string, ReservationState] | undefined {
  console.log("Searching for state with channelId:", channelId);
  console.log("Current states:", Array.from(reservationStates.entries()));

  for (const [stateId, state] of reservationStates.entries()) {
    console.log("Checking state:", { stateId, state });
    if (state.channelId === channelId && state.inputState !== "NONE") {
      console.log("Found matching state:", { stateId, state });
      return [stateId, state];
    }
  }
  console.log("No matching state found");
  return undefined;
}

// 기존 클래스 기반 인터페이스와의 호환성을 위한 객체
export const reservationStateManager = {
  createState: createReservationState,
  getState: getReservationState,
  updateState: updateReservationState,
  deleteState: deleteReservationState,
  getStateByMessageTs: getReservationStateByMessageTs,
  getStateByChannel: getReservationStateByChannel,
};
