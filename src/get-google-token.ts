import fs from "fs";
import { OAuth2Client } from "google-auth-library";
import readline from "readline";

async function main() {
  const credentials = JSON.parse(
    fs.readFileSync("client_secret.json", "utf-8")
  );
  const { client_id, client_secret, redirect_uris } = credentials.installed;
  const oAuth2Client = new OAuth2Client(
    client_id,
    client_secret,
    redirect_uris[0]
  );

  // const SCOPES = ["https://www.googleapis.com/auth/drive.readonly"]; // 필요에 따라 변경
  const SCOPES = [
    "https://www.googleapis.com/auth/calendar",
    "https://www.googleapis.com/auth/calendar.events",
  ];
  const authUrl = oAuth2Client.generateAuthUrl({
    access_type: "offline",
    scope: SCOPES,
  });

  console.log("다음 URL을 브라우저에서 열어주세요:\n", authUrl);
  // open(authUrl); // open은 ESM 전용이므로, 직접 브라우저에 URL을 복사해 여세요.

  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  rl.question("브라우저에서 인증 후 받은 코드를 입력하세요: ", async (code) => {
    rl.close();
    const { tokens } = await oAuth2Client.getToken(code);
    oAuth2Client.setCredentials(tokens);
    fs.writeFileSync("token.json", JSON.stringify(tokens, null, 2));
    console.log("토큰이 token.json 파일에 저장되었습니다:", tokens);
  });
}

main().catch(console.error);
