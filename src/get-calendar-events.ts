import fs from "fs";
import { google } from "googleapis";
import { formatRoomEvents } from "./formatRoomEvents";

async function main() {
  // 1. 토큰과 client_secret.json 불러오기
  const credentials = JSON.parse(
    fs.readFileSync("client_secret.json", "utf-8")
  );
  const token = JSON.parse(fs.readFileSync("token.json", "utf-8"));

  const { client_id, client_secret, redirect_uris } = credentials.installed;
  const oAuth2Client = new google.auth.OAuth2(
    client_id,
    client_secret,
    redirect_uris[0]
  );
  oAuth2Client.setCredentials(token);

  // 2. 구글 캘린더 API 클라이언트 생성
  const calendar = google.calendar({ version: "v3", auth: oAuth2Client });

  // 3. 캘린더 이벤트 조회 (예: 오늘~내일)
  const now = new Date();
  const tomorrow = new Date();
  tomorrow.setDate(now.getDate() + 1);

  const res = await calendar.events.list({
    calendarId: "primary",
    timeMin: now.toISOString(),
    timeMax: tomorrow.toISOString(),
    maxResults: 10,
    singleEvents: true,
    orderBy: "startTime",
  });

  const events = res.data.items;
  if (!events || events.length === 0) {
    console.log("일정이 없습니다.");
  } else {
    console.log("다가오는 일정:");
    const formattedEvents = await formatRoomEvents("Primary Calendar", events);
    console.log(formattedEvents);
  }
}

main().catch(console.error);
