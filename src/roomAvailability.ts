// 회의실 가용성 관련 유틸리티 함수들

// 특정 시간에 회의실이 사용 가능한지 확인하는 함수 (분 단위 지원)
export function isRoomAvailable(
  events: { start: string; end: string; summary: string }[],
  targetHour: number | undefined,
  roomName: string,
  targetMinute: number = 0
): boolean {
  if (targetHour === undefined) return true;

  console.log(
    `\n=== Checking availability for ${roomName} at ${targetHour}:${targetMinute.toString().padStart(2, "0")} ===`
  );

  // 시간 문자열 정규화 (HH:mm 형식으로 변환)
  function normalizeTimeStr(timeStr: string): string {
    const [hours, minutes] = timeStr
      .split(":")
      .map((num) => num.padStart(2, "0"));
    return `${hours}:${minutes || "00"}`;
  }

  // 각 이벤트에 대해 시간 중복 체크
  for (const event of events) {
    try {
      const normalizedStart = normalizeTimeStr(event.start);
      const normalizedEnd = normalizeTimeStr(event.end);

      console.log(`\nChecking event: "${event.summary}"`, {
        original: { start: event.start, end: event.end },
        normalized: { start: normalizedStart, end: normalizedEnd },
      });

      const [startHour, startMin] = normalizedStart.split(":").map(Number);
      const [endHour, endMin] = normalizedEnd.split(":").map(Number);

      // 종일 예약 체크 (00:00-00:00)
      if (startHour === 0 && startMin === 0 && endHour === 0 && endMin === 0) {
        console.log(`${roomName} is NOT available - All day reservation`);
        return false;
      }

      // 분 단위까지 정확한 시간 중복 체크
      const normalizedEndHour = endHour === 0 && endMin === 0 ? 24 : endHour;
      const normalizedEndMin = endHour === 0 && endMin === 0 ? 0 : endMin;

      // 시간을 분 단위로 변환하여 비교
      const eventStartMinutes = startHour * 60 + startMin;
      const eventEndMinutes = normalizedEndHour * 60 + normalizedEndMin;
      const targetMinutes = targetHour * 60 + targetMinute;

      console.log("Time comparison (minutes):", {
        startTime: `${startHour}:${startMin.toString().padStart(2, "0")} (${eventStartMinutes}분)`,
        endTime: `${normalizedEndHour}:${normalizedEndMin.toString().padStart(2, "0")} (${eventEndMinutes}분)`,
        targetTime: `${targetHour}:${targetMinute.toString().padStart(2, "0")} (${targetMinutes}분)`,
        condition: `${eventStartMinutes} <= ${targetMinutes} < ${eventEndMinutes}`,
        result:
          eventStartMinutes <= targetMinutes && targetMinutes < eventEndMinutes,
      });

      // 분 단위 시간 중복 체크
      const hasConflict =
        eventStartMinutes <= targetMinutes && targetMinutes < eventEndMinutes;

      if (hasConflict) {
        console.log(
          `${roomName} is NOT available at ${targetHour}:${targetMinute.toString().padStart(2, "0")} - Time conflict detected`
        );
        return false;
      } else {
        console.log(
          `✅ No conflict with event "${event.summary}" for ${targetHour}:${targetMinute.toString().padStart(2, "0")}`
        );
      }
    } catch (error) {
      console.error("Error processing event:", error);
    }
  }

  console.log(
    `${roomName} is AVAILABLE at ${targetHour}:${targetMinute.toString().padStart(2, "0")} ✅`
  );
  return true;
}

// 회의실의 예약 가능한 시간대를 찾는 함수
export function findAvailableTimeSlots(
  events: { start: string; end: string; summary: string }[],
  targetDate: string
): { start: string; end: string }[] {
  const slots: { start: string; end: string }[] = [];
  const workingHours = { start: 9, end: 22 }; // 9시~22시 업무시간

  // 종일 예약 체크 (00:00-00:00)
  const hasAllDayReservation = events.some((event) => {
    const normalizedStart = event.start.padStart(5, "0"); // "0:00" -> "00:00"
    const normalizedEnd = event.end.padStart(5, "0");
    return normalizedStart === "00:00" && normalizedEnd === "00:00";
  });

  if (hasAllDayReservation) {
    console.log("🚫 종일 예약이 있어 예약 불가능합니다.");
    return []; // 종일 예약이 있으면 빈 배열 반환
  }

  // 오늘이면 현재 시간 이후부터, 다른 날이면 업무시간 시작부터
  const today = new Date().toISOString().split("T")[0];
  const currentHour = new Date().getHours();

  console.log("📅 날짜 비교:", {
    targetDate,
    today,
    isToday: targetDate === today,
    currentHour,
  });

  const startHour =
    targetDate === today
      ? Math.max(currentHour + 1, workingHours.start)
      : workingHours.start;

  console.log("⏰ 검색 시작 시간:", startHour);

  // 예약된 시간들을 정렬
  const sortedEvents = events
    .map((event) => ({
      start: parseInt(event.start.split(":")[0]),
      end: parseInt(event.end.split(":")[0]) || 24,
      summary: event.summary,
    }))
    .sort((a, b) => a.start - b.start);

  console.log("📋 정렬된 이벤트들:", sortedEvents);

  let currentTime = startHour;

  for (const event of sortedEvents) {
    // 현재 시간부터 이벤트 시작 전까지 빈 시간이 있으면 추가
    if (currentTime < event.start && currentTime < workingHours.end) {
      const slotEnd = Math.min(event.start, workingHours.end);
      if (slotEnd > currentTime) {
        console.log(
          `✅ 예약 가능 시간 찾음: ${currentTime}:00 ~ ${slotEnd}:00`
        );
        slots.push({
          start: `${currentTime.toString().padStart(2, "0")}:00`,
          end: `${slotEnd.toString().padStart(2, "0")}:00`,
        });
      }
    }
    // 현재 시간을 이벤트 종료 시간으로 업데이트
    currentTime = Math.max(currentTime, event.end);
  }

  // 마지막 이벤트 이후부터 업무시간 종료까지 빈 시간 추가
  if (currentTime < workingHours.end) {
    console.log(
      `✅ 마지막 예약 가능 시간: ${currentTime}:00 ~ ${workingHours.end}:00`
    );
    slots.push({
      start: `${currentTime.toString().padStart(2, "0")}:00`,
      end: `${workingHours.end.toString().padStart(2, "0")}:00`,
    });
  }

  console.log("🎯 최종 예약 가능 시간대들:", slots);
  return slots;
}

// 특정 시간부터 예약 가능한 최대 시간을 계산하는 함수 (분 단위까지 정확히 계산)
export function getMaxAvailableTime(
  events: { start: string; end: string; summary: string }[],
  startHour: number,
  startMinute: number = 0
): { hour: number; minute: number } {
  console.log(
    `🔍 getMaxAvailableTime 호출: ${startHour}:${startMinute.toString().padStart(2, "0")}부터 계산`
  );

  // 시작 시간을 분 단위로 변환
  const startTimeInMinutes = startHour * 60 + startMinute;

  // 시작 시간 이후의 모든 예약을 찾기 (분 단위로 정확히 비교)
  const futureEvents = events
    .map((event) => {
      const [eventStartHour, eventStartMinute = 0] = event.start
        .split(":")
        .map(Number);
      const eventStartInMinutes = eventStartHour * 60 + eventStartMinute;
      return {
        ...event,
        startInMinutes: eventStartInMinutes,
        startHour: eventStartHour,
        startMinute: eventStartMinute,
      };
    })
    .filter((event) => event.startInMinutes > startTimeInMinutes)
    .sort((a, b) => a.startInMinutes - b.startInMinutes);

  console.log(
    "📋 시작 시간 이후 예약들:",
    futureEvents.map((e) => ({
      summary: e.summary,
      start: `${e.startHour}:${e.startMinute.toString().padStart(2, "0")}`,
      startInMinutes: e.startInMinutes,
    }))
  );

  // 가장 가까운 다음 예약이 없으면 하루 끝(22:00)까지 가능
  if (futureEvents.length === 0) {
    console.log("✅ 다음 예약 없음, 22:00까지 예약 가능");
    return { hour: 22, minute: 0 };
  }

  // 가장 빠른 다음 예약의 시작 시간
  const nextEvent = futureEvents[0];
  console.log(
    `⏰ 다음 예약: ${nextEvent.startHour}:${nextEvent.startMinute.toString().padStart(2, "0")} (${nextEvent.summary})`
  );

  return {
    hour: nextEvent.startHour,
    minute: nextEvent.startMinute,
  };
}

// 회의 지속 시간 선택 옵션을 생성하는 함수 (분 단위 정확 계산)
export function createDurationOptions(
  startHour: number,
  startMinute: number,
  maxEndTime: { hour: number; minute: number }
): Array<{ duration: number; label: string }> {
  const options = [];

  // 시작 시간과 최대 종료 시간을 분 단위로 변환
  const startTimeInMinutes = startHour * 60 + startMinute;
  const maxEndTimeInMinutes = maxEndTime.hour * 60 + maxEndTime.minute;
  const maxDuration = maxEndTimeInMinutes - startTimeInMinutes;

  console.log(
    `⏱️ 지속시간 옵션 생성: ${startHour}:${startMinute.toString().padStart(2, "0")} ~ ${maxEndTime.hour}:${maxEndTime.minute.toString().padStart(2, "0")} (최대 ${maxDuration}분)`
  );

  // 최소 예약 단위(30분) 미만이면 옵션 생성 X
  if (maxDuration < 30) {
    console.log("최소 예약 단위 미만이므로 옵션 없음");
    return [];
  }

  // 30분 단위로 옵션 생성 (최소 30분, 최대 가능한 시간까지)
  for (let minutes = 30; minutes <= maxDuration; minutes += 30) {
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;

    let label = "";
    if (hours > 0 && remainingMinutes > 0) {
      label = `${hours}시간 ${remainingMinutes}분`;
    } else if (hours > 0) {
      label = `${hours}시간`;
    } else {
      label = `${remainingMinutes}분`;
    }

    options.push({ duration: minutes, label });
  }

  console.log(`📝 생성된 옵션들: ${options.map((o) => o.label).join(", ")}`);
  return options;
}

// 시간 변환 함수
export function convertTo24Hour(hour: number): number {
  // 9시 이전 시간은 오후로 처리 (1시~9시 → 13시~21시)
  if (hour >= 1 && hour <= 9) {
    return hour + 12;
  }
  // 10시, 11시, 12시는 그대로 처리
  return hour;
}
