// 예약/추천/확정 등에서 반복적으로 쓰이는 공통 유틸 함수 모음

/**
 * 구글 캘린더 URL을 생성합니다.
 * @param roomName 회의실 이름
 * @param userName 예약자 이름
 * @param startDate 시작 Date 객체
 * @param endDate 종료 Date 객체
 * @param calendarId 구글 캘린더 리소스 ID
 */
export function makeCalendarUrl({
  roomName,
  userName,
  startDate,
  endDate,
  calendarId,
}: {
  roomName: string;
  userName: string;
  startDate: Date;
  endDate: Date;
  calendarId: string;
}): string {
  const startTimeFormatted = formatDateForGoogle(startDate);
  const endTimeFormatted = formatDateForGoogle(endDate);
  return `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${encodeURIComponent(`[${roomName}] ${userName}`)}&dates=${startTimeFormatted}/${endTimeFormatted}&details=${encodeURIComponent(`주최자: ${userName}\n회의실: ${roomName}\nSlack에서 예약됨`)}&add=${encodeURIComponent(calendarId)}`;
}

/**
 * 룸 아이콘 변환
 */
export function makeRoomIcon(roomName: string): string {
  if (roomName.includes("The1")) return ":one:";
  if (roomName.includes("The2")) return ":two:";
  if (roomName.includes("The3")) return ":three:";
  if (roomName.includes("The4")) return ":four:";
  if (roomName.includes("Light")) return ":sunny:";
  if (roomName.includes("Dark")) return ":new_moon:";
  if (roomName.includes("East")) return ":arrow_right:";
  if (roomName.includes("West")) return ":arrow_left:";
  return ":large_blue_square:";
}

/**
 * 구글 캘린더용 ISO 포맷 문자열로 변환
 */
export function formatDateForGoogle(date: Date): string {
  return date
    .toISOString()
    .replace(/[-:]/g, "")
    .replace(/\.\d{3}/, "");
}

/**
 * 예약 메시지 텍스트 생성
 */
export function makeReservationMessage({
  roomName,
  userName,
  date,
  startTime,
  endTime,
  durationMinutes,
  isUnavailable = false,
}: {
  roomName: string;
  userName: string;
  date: string;
  startTime: string;
  endTime: string | null;
  durationMinutes: number;
  isUnavailable?: boolean;
}): string {
  // 🆕 예약 불가 시 다른 형식으로 표시
  if (isUnavailable) {
    return `:x: *${roomName}* 회의실 예약 불가\n📆 *요청 날짜* : ${date}\n🕐 *요청 시간* : ${startTime}${endTime ? ` ~ ${endTime} (${makeDurationText(durationMinutes)})` : ""}\n🚫 해당시간은 현재 *예약이 불가능한* 시간입니다. 다른 시간을 선택해주세요.`;
  }

  if (endTime === null)
    return `${makeRoomIcon(roomName)} *${roomName}* 회의실 예약을 진행합니다.\n\n👤 주최자: ${userName}\n\n📅 날짜: ${date}\n\n⏰ 시간: ${startTime} (${makeDurationText(durationMinutes)})`;
  return `${makeRoomIcon(roomName)} *${roomName}* 회의실 예약을 진행합니다.\n\n👤 주최자: ${userName}\n\n📅 날짜: ${date}\n\n⏰ 시간: ${startTime} ~ ${endTime} (${makeDurationText(durationMinutes)})`;
}

/**
 * 분 단위 시간을 "1시간 30분" 등으로 변환
 */
export function makeDurationText(durationMinutes: number): string {
  if (durationMinutes >= 60) {
    const hours = Math.floor(durationMinutes / 60);
    const mins = durationMinutes % 60;
    return mins > 0 ? `${hours}시간 ${mins}분` : `${hours}시간`;
  }
  return `${durationMinutes}분`;
}

/**
 * 날짜를 한국어 형식으로 포맷팅 (MM월 DD일 (요일))
 */
export function formatDateKorean(date: string | Date): string {
  const dateObj = typeof date === "string" ? new Date(date) : date;
  const month = dateObj.getMonth() + 1;
  const day = dateObj.getDate();
  const dayOfWeek = ["일", "월", "화", "수", "목", "금", "토"][
    dateObj.getDay()
  ];
  return `${month}월 ${day}일 (${dayOfWeek})`;
}

/**
 * 지속시간 검증 및 에러 메시지 생성
 */
export function validateDurationAndCreateErrorMessage({
  requestedDuration,
  maxAvailableMinutes,
  roomName,
  date,
  startTime,
  nextReservationTime,
}: {
  requestedDuration: number;
  maxAvailableMinutes: number;
  roomName: string;
  date: string;
  startTime: string;
  nextReservationTime: string;
}): { isValid: boolean; errorMessage?: string; endTime?: string } {
  if (requestedDuration <= maxAvailableMinutes) {
    return { isValid: true };
  }

  // 요청한 지속시간과 가능한 최대 시간을 포맷팅
  const reqDurationText = makeDurationText(requestedDuration);
  const maxDurationText = makeDurationText(maxAvailableMinutes);

  // 종료 시간 계산
  const startMinutes =
    parseInt(startTime.split(":")[0]) * 60 + parseInt(startTime.split(":")[1]);
  const endMinutes = startMinutes + requestedDuration;
  const endHour = Math.floor(endMinutes / 60);
  const endMin = endMinutes % 60;
  const endTime = `${endHour.toString().padStart(2, "0")}:${endMin.toString().padStart(2, "0")}`;

  // 날짜 포맷팅
  const formattedDate = formatDateKorean(date);

  const errorMessage =
    `:x: *${roomName}* 회의실 예약 불가\n\n` +
    `📅 *요청 날짜*: ${formattedDate}\n` +
    `🕐 *요청 시간*: ${startTime} ~ ${endTime} (${reqDurationText})\n\n` +
    `⚠️ *${nextReservationTime}*부터 다른 예약이 있어 최대 *${maxDurationText}*만 예약 가능합니다.\n\n` +
    `💡 *${maxDurationText} 예약*을 원하시면 다시 요청해주세요.`;

  return {
    isValid: false,
    errorMessage,
    endTime,
  };
}
