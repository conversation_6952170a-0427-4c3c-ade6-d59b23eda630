import { format, parse, isValid } from "date-fns";
import { ko } from "date-fns/locale";

export interface TimeRange {
  start: string;
  end: string;
}

type TimeHandler = (...args: string[]) => string | null;

interface TimePattern {
  regex: RegExp;
  handler: TimeHandler;
}

export function parseTimeInput(input: string): string | null {
  // Remove all spaces and convert to lowercase
  const cleanInput = input.replace(/\s+/g, "").toLowerCase();

  // Try various time formats
  const patterns: TimePattern[] = [
    // 시간과 지속시간이 함께 있는 경우 (13:00(1시간), 14시(30분) 등) - 시간 부분만 추출
    {
      regex: /^(\d{1,2}):(\d{2})\([^)]*\)$/,
      handler: (h: string, m: string) => {
        const hours = parseInt(h);
        const minutes = parseInt(m);
        if (hours >= 0 && hours < 24 && minutes >= 0 && minutes < 60) {
          return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}`;
        }
        return null;
      },
    },
    {
      regex: /^(\d{1,2})시\([^)]*\)$/,
      handler: (h: string) => {
        const hours = parseInt(h);
        if (hours >= 0 && hours < 24) {
          return `${hours.toString().padStart(2, "0")}:00`;
        }
        return null;
      },
    },
    // 24시간 형식 (14:30, 14시30분, 20:00)
    {
      regex: /^(\d{1,2}):(\d{2})$/,
      handler: (h: string, m: string) => {
        const hours = parseInt(h);
        const minutes = parseInt(m);
        if (hours >= 0 && hours < 24 && minutes >= 0 && minutes < 60) {
          return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}`;
        }
        return null;
      },
    },
    // 4자리 숫자 형식 (2000, 1430)
    {
      regex: /^(\d{2})(\d{2})$/,
      handler: (h: string, m: string) => {
        const hours = parseInt(h);
        const minutes = parseInt(m);
        if (hours >= 0 && hours < 24 && minutes >= 0 && minutes < 60) {
          return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}`;
        }
        return null;
      },
    },
    // 한글 시간 표현들
    {
      regex: /^(\d{1,2})시(\d{1,2})분$/,
      handler: (h: string, m: string) => {
        const hours = parseInt(h);
        const minutes = parseInt(m);
        if (hours >= 0 && hours < 24 && minutes >= 0 && minutes < 60) {
          return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}`;
        }
        return null;
      },
    },
    {
      regex: /^(\d{1,2})시반$/,
      handler: (h: string) => {
        const hours = parseInt(h);
        if (hours >= 0 && hours < 24) {
          return `${hours.toString().padStart(2, "0")}:30`;
        }
        return null;
      },
    },
    // 시간만 있는 경우 (14시, 14, 20)
    {
      regex: /^(\d{1,2})시?$/,
      handler: (h: string) => {
        const hours = parseInt(h);
        if (hours >= 0 && hours < 24) {
          return `${hours.toString().padStart(2, "0")}:00`;
        }
        return null;
      },
    },
    // 숫자만 입력한 경우 (20, 14 등)
    {
      regex: /^(\d{1,2})$/,
      handler: (h: string) => {
        const hours = parseInt(h);
        if (hours >= 0 && hours < 24) {
          return `${hours.toString().padStart(2, "0")}:00`;
        }
        return null;
      },
    },
    // 오전/오후 형식들
    {
      regex: /^(오전|오후)(\d{1,2}):(\d{1,2})$/,
      handler: (ampm: string, h: string, m: string) =>
        formatAMPMTime(ampm, parseInt(h), parseInt(m)),
    },
    {
      regex: /^(오전|오후)(\d{1,2})시(\d{1,2})분$/,
      handler: (ampm: string, h: string, m: string) =>
        formatAMPMTime(ampm, parseInt(h), parseInt(m)),
    },
    {
      regex: /^(오전|오후)(\d{1,2})시반$/,
      handler: (ampm: string, h: string) =>
        formatAMPMTime(ampm, parseInt(h), 30),
    },
    {
      regex: /^(오전|오후)(\d{1,2})시?$/,
      handler: (ampm: string, h: string) =>
        formatAMPMTime(ampm, parseInt(h), 0),
    },
    // 한국어 숫자 표현들
    {
      regex: /^(한|두|세|네|다섯|여섯|일곱|여덟|아홉|열|열한|열두)시$/,
      handler: (korean: string) => {
        const koreanNumbers: { [key: string]: number } = {
          한: 1,
          두: 2,
          세: 3,
          네: 4,
          다섯: 5,
          여섯: 6,
          일곱: 7,
          여덟: 8,
          아홉: 9,
          열: 10,
          열한: 11,
          열두: 12,
        };
        const hours = koreanNumbers[korean];
        if (hours) {
          return `${hours.toString().padStart(2, "0")}:00`;
        }
        return null;
      },
    },
    {
      regex:
        /^(오전|오후)(한|두|세|네|다섯|여섯|일곱|여덟|아홉|열|열한|열두)시$/,
      handler: (ampm: string, korean: string) => {
        const koreanNumbers: { [key: string]: number } = {
          한: 1,
          두: 2,
          세: 3,
          네: 4,
          다섯: 5,
          여섯: 6,
          일곱: 7,
          여덟: 8,
          아홉: 9,
          열: 10,
          열한: 11,
          열두: 12,
        };
        const hours = koreanNumbers[korean];
        if (hours) {
          return formatAMPMTime(ampm, hours, 0);
        }
        return null;
      },
    },
    {
      regex: /^(한|두|세|네|다섯|여섯|일곱|여덟|아홉|열|열한|열두)시반$/,
      handler: (korean: string) => {
        const koreanNumbers: { [key: string]: number } = {
          한: 1,
          두: 2,
          세: 3,
          네: 4,
          다섯: 5,
          여섯: 6,
          일곱: 7,
          여덟: 8,
          아홉: 9,
          열: 10,
          열한: 11,
          열두: 12,
        };
        const hours = koreanNumbers[korean];
        if (hours) {
          return `${hours.toString().padStart(2, "0")}:30`;
        }
        return null;
      },
    },
    {
      regex:
        /^(오전|오후)(한|두|세|네|다섯|여섯|일곱|여덟|아홉|열|열한|열두)시반$/,
      handler: (ampm: string, korean: string) => {
        const koreanNumbers: { [key: string]: number } = {
          한: 1,
          두: 2,
          세: 3,
          네: 4,
          다섯: 5,
          여섯: 6,
          일곱: 7,
          여덟: 8,
          아홉: 9,
          열: 10,
          열한: 11,
          열두: 12,
        };
        const hours = koreanNumbers[korean];
        if (hours) {
          return formatAMPMTime(ampm, hours, 30);
        }
        return null;
      },
    },
  ];

  for (const { regex, handler } of patterns) {
    const match = cleanInput.match(regex);
    if (match) {
      const [, ...groups] = match;
      const result = handler(...groups);
      if (result && isValidTime(result)) {
        return result;
      }
    }
  }

  return null;
}

function formatAMPMTime(
  ampm: string,
  hours: number,
  minutes: number
): string | null {
  if (hours < 0 || hours > 12 || minutes < 0 || minutes >= 60) return null;

  if (ampm === "오후" && hours !== 12) hours += 12;
  if (ampm === "오전" && hours === 12) hours = 0;

  return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}`;
}

function isValidTime(time: string): boolean {
  const [hours, minutes] = time.split(":").map(Number);
  return hours >= 0 && hours < 24 && minutes >= 0 && minutes < 60;
}

export function formatTimeRange(start: string, end: string): string {
  return `${start}~${end}`;
}

type DurationHandler = (...args: string[]) => number;

interface DurationPattern {
  regex: RegExp;
  handler?: DurationHandler;
  multiplier?: number;
}

export function parseDuration(input: string): number | null {
  console.log("=== parseDuration 함수 실행 ===");
  console.log("원본 입력:", input);
  const cleanInput = input.replace(/\s+/g, "").toLowerCase();
  console.log("정리된 입력:", cleanInput);

  // 한국어 숫자 매핑
  const koreanNumbers: { [key: string]: number } = {
    한: 1,
    두: 2,
    세: 3,
    네: 4,
    다섯: 5,
    여섯: 6,
    일곱: 7,
    여덟: 8,
    아홉: 9,
    열: 10,
    스무: 20,
    서른: 30,
    마흔: 40,
    쉰: 50,
  };

  const patterns: DurationPattern[] = [
    // 아라비아 숫자
    { regex: /(\d+)시간/, multiplier: 60 },
    { regex: /(\d+)분/, multiplier: 1 },
    {
      regex: /(\d+)시간(\d+)분/,
      handler: (h: string, m: string) => parseInt(h) * 60 + parseInt(m),
    },
    // 한국어 숫자
    {
      regex: /(한|두|세|네|다섯|여섯|일곱|여덟|아홉|열|스무|서른|마흔|쉰)시간/,
      handler: (korean: string) => koreanNumbers[korean] * 60,
    },
    {
      regex: /(한|두|세|네|다섯|여섯|일곱|여덟|아홉|열|스무|서른|마흔|쉰)분/,
      handler: (korean: string) => koreanNumbers[korean],
    },
    // 한국어 숫자 조합 (예: 한시간삼십분)
    {
      regex:
        /(한|두|세|네|다섯|여섯|일곱|여덟|아홉|열)시간(한|두|세|네|다섯|여섯|일곱|여덟|아홉|열|스무|서른|마흔|쉰)분/,
      handler: (h: string, m: string) =>
        koreanNumbers[h] * 60 + koreanNumbers[m],
    },
    // 한시간반, 두시간반 등
    {
      regex: /(한|두|세|네|다섯|여섯|일곱|여덟|아홉|열)시간반/,
      handler: (h: string) => koreanNumbers[h] * 60 + 30,
    },
  ];

  for (const pattern of patterns) {
    const match = cleanInput.match(pattern.regex);
    console.log("패턴 테스트:", pattern.regex, "매치 결과:", match);
    if (match) {
      console.log("매치 성공! 그룹:", match);
      if (pattern.handler) {
        const [, ...groups] = match;
        const result = pattern.handler(...groups);
        console.log("핸들러 결과:", result);
        return result;
      } else {
        const result = parseInt(match[1]) * pattern.multiplier!;
        console.log("multiplier 결과:", result);
        return result;
      }
    }
  }

  console.log("모든 패턴 매치 실패");
  return null;
}

export function formatDuration(minutes: number): string {
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;

  if (hours === 0) {
    return `${remainingMinutes}분`;
  } else if (remainingMinutes === 0) {
    return `${hours}시간`;
  } else {
    return `${hours}시간 ${remainingMinutes}분`;
  }
}

export function isTimeInRange(time: string, range: TimeRange): boolean {
  console.log("Checking time range:", { time, range });

  // 시간을 분으로 변환하여 비교
  const [timeHours, timeMinutes] = time.split(":").map(Number);
  const [startHours, startMinutes] = range.start.split(":").map(Number);
  const [endHours, endMinutes] = range.end.split(":").map(Number);

  const timeInMinutes = timeHours * 60 + timeMinutes;
  const startInMinutes = startHours * 60 + startMinutes;
  const endInMinutes = endHours * 60 + endMinutes;

  console.log("Time comparison (in minutes):", {
    time: timeInMinutes,
    start: startInMinutes,
    end: endInMinutes,
  });

  return timeInMinutes >= startInMinutes && timeInMinutes <= endInMinutes;
}

/**
 * HH:MM 문자열에 분을 더해 새로운 HH:MM 반환
 */
export function addMinutesToTime(time: string, minutes: number): string {
  const [hours, mins] = time.split(":").map(Number);
  const totalMinutes = hours * 60 + mins + minutes;
  const newHours = Math.floor(totalMinutes / 60);
  const newMinutes = totalMinutes % 60;
  return `${newHours.toString().padStart(2, "0")}:${newMinutes.toString().padStart(2, "0")}`;
}
