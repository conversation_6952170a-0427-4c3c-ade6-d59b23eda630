import { WebClient } from "@slack/web-api";
import "dotenv/config";
import { makeRoomIcon } from "./utils/reservationUtils";

const slackClient = new WebClient(process.env.SLACK_BOT_TOKEN);

// 이메일로 Slack 사용자 정보 조회
export async function getSlackUsername(email: string): Promise<string> {
  try {
    const result = await slackClient.users.lookupByEmail({
      email: email,
    });

    if (result.ok && result.user) {
      return (
        result.user.profile?.display_name ||
        result.user.real_name ||
        result.user.name ||
        email
      );
    }
    return email;
  } catch (error) {
    console.log(
      `Could not find Slack user for email ${email}, using email instead`
    );
    return email;
  }
}

// 참석자 정보를 displayName 우선으로 처리하는 함수
export async function formatAttendeeInfo(attendee: {
  email: string;
  displayName?: string;
}): Promise<string> {
  // displayName이 있으면 우선 사용
  if (attendee.displayName && attendee.displayName.trim()) {
    return attendee.displayName;
  }

  // displayName이 없으면 슬랙 사용자 정보 조회
  const slackName = await getSlackUsername(attendee.email);

  // 슬랙에서 찾은 이름이 이메일과 다르면 (즉, 실제 이름을 찾았으면) 사용
  if (slackName !== attendee.email) {
    return slackName;
  }

  // 모든 방법이 실패하면 이메일 주소 반환
  return attendee.email;
}

// 회의실 일정 결과를 보기 좋게 포맷팅하는 함수 (슬랙/터미널 공용)
export async function formatRoomEvents(roomName: string, events: any[]) {
  // 시간 포맷: HH:MM
  function formatTime(dateStr?: string) {
    if (!dateStr) return "";
    const date = new Date(dateStr);
    const hh = String(date.getHours()).padStart(2, "0");
    const min = String(date.getMinutes()).padStart(2, "0");
    return `${hh}:${min}`;
  }

  // 날짜 포맷: YYYY-MM-DD
  function formatDate(dateStr?: string) {
    if (!dateStr) return "";
    const date = new Date(dateStr);
    const yyyy = date.getFullYear();
    const mm = String(date.getMonth() + 1).padStart(2, "0");
    const dd = String(date.getDate()).padStart(2, "0");
    return `${yyyy}-${mm}-${dd}`;
  }

  // 참석자 정보 포맷
  async function formatPerson(person?: {
    displayName?: string;
    email?: string;
  }) {
    if (!person) return "알 수 없음";
    if (!person.email) return person.displayName || "알 수 없음";

    const formattedName = await formatAttendeeInfo({
      email: person.email,
      displayName: person.displayName,
    });

    return formattedName;
  }

  // 일정이 없을 때 메시지
  if (!events.length) {
    return `[📌 ${roomName}]
  - 예약 없음`;
  }

  // 각 이벤트를 보기 좋게 포맷팅
  const formattedEvents = await Promise.all(
    events.map(async (ev) => {
      const start = ev.start?.dateTime || ev.start?.date;
      const end = ev.end?.dateTime || ev.end?.date;
      const dateStr = formatDate(start);
      const timeStr = `${formatTime(start)} ~ ${formatTime(end)}`;
      const organizer = await formatPerson(ev.organizer);
      const attendees =
        ev.attendees && ev.attendees.length
          ? (
              await Promise.all(ev.attendees.map((a: any) => formatPerson(a)))
            ).join(", ")
          : "없음";
      return `  [${dateStr}] ${timeStr} : ${ev.summary}\n- 주최자: ${organizer}\n- 참석자: ${attendees}`;
    })
  );

  // 전체 결과 반환
  return `[📌 ${roomName}]
${formattedEvents.join("\n")}`;
}

// Block Kit용 데이터 가공
export async function getRoomReservationBlockKit(
  targetDate?: string,
  roomData?: {
    [roomName: string]: {
      events: {
        start: string;
        end: string;
        summary: string;
        attendees: { email: string; displayName?: string }[];
      }[];
    };
  }
) {
  if (!roomData) return [];

  // 각 이벤트의 참석자 정보를 처리하고 string 배열로 변환
  const processedRoomData: {
    [roomName: string]: {
      events: {
        start: string;
        end: string;
        summary: string;
        attendees: string[];
      }[];
    };
  } = {};

  for (const roomName of Object.keys(roomData)) {
    const events = roomData[roomName].events;
    const processedEvents = [];

    for (const event of events) {
      // 모든 참석자 정보를 적절히 포맷팅
      const attendeeNames = await Promise.all(
        event.attendees.map(async (attendee) => {
          return await formatAttendeeInfo(attendee);
        })
      );

      processedEvents.push({
        ...event,
        attendees: attendeeNames,
      });
    }

    processedRoomData[roomName] = {
      events: processedEvents,
    };
  }

  // 날짜 문자열
  let dateStr = "";
  if (targetDate && targetDate.includes("~")) {
    dateStr = targetDate;
  } else if (targetDate) {
    const dateObj = new Date(targetDate);
    const yyyy = dateObj.getFullYear();
    const mm = String(dateObj.getMonth() + 1).padStart(2, "0");
    const dd = String(dateObj.getDate()).padStart(2, "0");
    dateStr = `${yyyy}-${mm}-${dd}`;
  } else {
    const dateObj = new Date();
    const yyyy = dateObj.getFullYear();
    const mm = String(dateObj.getMonth() + 1).padStart(2, "0");
    const dd = String(dateObj.getDate()).padStart(2, "0");
    dateStr = `${yyyy}-${mm}-${dd}`;
  }

  return buildReservationBlocksV2(dateStr, processedRoomData);
}

// Block Kit 생성 함수
export function buildReservationBlocksV2(
  dateStr: string,
  roomData: {
    [roomName: string]: {
      events: {
        start: string;
        end: string;
        summary: string;
        attendees: string[];
      }[];
    };
  }
) {
  const blocks: any[] = [];

  // 상단 헤더
  blocks.push({
    type: "section",
    text: {
      type: "mrkdwn",
      text: `:calendar: *회의실 현황* (*${dateStr}*)`,
    },
  });

  blocks.push({ type: "divider" });

  // 회의실별로 출력
  for (const [roomName, data] of Object.entries(roomData)) {
    // 회의실명
    blocks.push({
      type: "section",
      text: {
        type: "mrkdwn",
        text: `${makeRoomIcon(roomName)} *${roomName}*`,
      },
    });

    if (data.events.length === 0) {
      // 비어있음
      blocks.push({
        type: "context",
        elements: [
          {
            type: "mrkdwn",
            text: ":white_check_mark: 현재 비어 있음!",
          },
        ],
      });
    } else {
      // 여러날(기간)일 때만 날짜 노출
      const isRange = dateStr.includes("~");
      for (const event of data.events) {
        let dateLabel = "";
        if (isRange) {
          if ("date" in event && event.date) {
            dateLabel = `*${event.date}*`;
          } else if (
            typeof event.start === "string" &&
            event.start.length >= 10
          ) {
            dateLabel = `*${event.start.slice(0, 10)}*`;
          }
        }
        blocks.push({
          type: "context",
          elements: [
            {
              type: "mrkdwn",
              text: `• ${dateLabel ? dateLabel + " " : ""}\`${event.start}~${event.end}\`  –  *${event.summary}*  ${event.attendees.length ? `| 참석자: ${event.attendees.join(", ")}` : ""}`,
            },
          ],
        });
      }
    }
    blocks.push({ type: "divider" });
  }

  return blocks;
}
