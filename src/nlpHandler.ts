import { format } from "date-fns";
import { ko } from "date-fns/locale";

interface MessageIntent {
  date?: Date;
  time?: number;
  isAvailabilityCheck: boolean;
  isReservationRequest: boolean;
  roomName?: string;
  needsRoomSelection?: boolean; // Focus Room East/West 선택이 필요한 경우
  possibleRooms?: string[]; // 가능한 회의실 목록
}

interface ParseResult {
  date: string | null;
  time: string | null;
  room: string | null;
  isAvailabilityCheck: boolean;
  isReservationRequest: boolean;
  isRoomInfoRequest?: boolean; // 회의실 정보 요청 여부
  needsRoomSelection?: boolean;
  possibleRooms?: string[];
  attendeeCount?: number; // 참석 인원수 추가
  duration?: number; // 예약 지속시간 (분 단위) 추가
  isGreetingOrChitchat?: boolean;
  geminiReply?: string;
  // 🆕 고급 의도 분류 관련 필드들
  isMyMeetingRequest?: boolean; // 내 회의 조회 요청
  needsSubjectConfirmation?: boolean; // 주체 확인 필요
  confidence?: number; // 분류 신뢰도
  reasoning?: string; // 분류 근거
}

// 회의실 이름 매핑 테이블
const ROOM_NAME_MAPPINGS: { [key: string]: string[] } = {
  The1: [
    "the1",
    "the 1",
    "더원",
    "더 원",
    "1번",
    "1호",
    "더1",
    "더 1",
    "the원",
    "the 원",
  ],
  The2: [
    "the2",
    "the 2",
    "더투",
    "더 투",
    "2번",
    "2호",
    "더2",
    "더 2",
    "the투",
    "the 투",
  ],
  The3: [
    "the3",
    "the 3",
    "더쓰리",
    "더 쓰리",
    "3번",
    "3호",
    "더3",
    "더 3",
    "the쓰리",
    "the 쓰리",
  ],
  The4: [
    "the4",
    "the 4",
    "더포",
    "더 포",
    "4번",
    "4호",
    "더4",
    "더 4",
    "the포",
    "the 포",
  ],
  "Light Room": [
    "light",
    "light room",
    "라이트",
    "라이트룸",
    "라이트 룸",
    "밝은",
    "밝은방",
    "라잇",
    "라잇룸",
    "라잇 룸",
  ],
  "Dark Room": [
    "dark",
    "dark room",
    "다크",
    "다크룸",
    "다크 룸",
    "어두운",
    "어두운방",
  ],
  "Focus Room (East)": [
    "focus east",
    "포커스룸 이스트",
    "포커스 룸 이스트",
    "집중룸 동쪽",
    "포커스동",
    "포커스동편",
    "포커스 동",
    "포커스 동편",
    "포커스 동쪽",
    "포커스룸동",
    "포커스룸 동",
    "포커스룸 동쪽",
    "포커스룸 동편",
  ],
  "Focus Room (West)": [
    "focus west",
    "포커스 웨스트",
    "포커스룸 웨스트",
    "포커스 룸 웨스트",
    "집중룸 서쪽",
    "포커스서",
    "포커스서편",
    "포커스 서편",
    "포커스 서쪽",
    "포커스룸서",
    "포커스룸서편",
    "포커스룸 서",
    "포커스룸 서쪽",
    "포커스룸 서편",
  ],
};

// 회의실 이름을 유연하게 찾는 함수
function findRoomName(message: string): {
  roomName?: string;
  needsSelection?: boolean;
  possibleRooms?: string[];
} {
  const lowerMessage = message.toLowerCase();

  // 정확한 매칭 먼저 시도
  for (const [officialName, aliases] of Object.entries(ROOM_NAME_MAPPINGS)) {
    if (aliases.some((alias) => lowerMessage.includes(alias.toLowerCase()))) {
      return { roomName: officialName };
    }
  }

  // Focus Room 일반적인 언급 (East/West 구분 없음)
  const focusKeywords = [
    "focus",
    "포커스",
    "집중",
    "집중룸",
    "포커스 룸",
    "포커스룸",
  ];

  // 포커스 키워드가 있는지 확인
  const hasFocusKeyword = focusKeywords.some((keyword) =>
    lowerMessage.includes(keyword)
  );

  if (hasFocusKeyword) {
    // East나 West가 명시되지 않은 경우
    const hasEast =
      lowerMessage.includes("east") ||
      lowerMessage.includes("이스트") ||
      lowerMessage.includes("동쪽") ||
      lowerMessage.includes("동편");
    const hasWest =
      lowerMessage.includes("west") ||
      lowerMessage.includes("웨스트") ||
      lowerMessage.includes("서쪽") ||
      lowerMessage.includes("서편");

    if (!hasEast && !hasWest) {
      // 단순히 예약 현황이나 상태 조회인 경우에는 포커스룸 선택 불필요
      const isStatusCheck =
        lowerMessage.includes("현황") ||
        lowerMessage.includes("상황") ||
        lowerMessage.includes("상태") ||
        lowerMessage.includes("조회") ||
        (lowerMessage.includes("예약") && !lowerMessage.includes("예약해")) ||
        lowerMessage.includes("알려줘") ||
        lowerMessage.includes("보여줘");

      // 상태 조회가 아니고 구체적인 예약 요청인 경우에만 포커스룸 선택 필요
      if (!isStatusCheck) {
        return {
          needsSelection: true,
          possibleRooms: ["Focus Room (East)", "Focus Room (West)"],
        };
      }
    }
  }

  return {};
}

// [내회의관련-수정] '내 회의' intent를 위한 핵심 키워드 (AI 분류 폴백용)
export const MY_MEETING_KEYWORDS = [
  "내 회의",
  "내 예약",
  "내예약",
  "나 회의",
  "나 예약",
  "내가 예약한",
  "내가 참석",
  "내 미팅",
  "나 미팅",
];

// [내회의관련-추가] 내 회의 관련 패턴 매칭 함수 (더 유연한 검사)
function hasMyMeetingPattern(message: string): boolean {
  const lowerMessage = message.toLowerCase();

  // 핵심 패턴: "내/나" + "회의/예약/미팅" 관련 단어
  const subjectPatterns = ["내", "나", "내가", "나의"];
  const meetingPatterns = ["회의", "예약", "미팅", "일정", "스케줄"];

  // 주체(내/나)가 있는지 확인
  const hasSubject = subjectPatterns.some((subject) =>
    lowerMessage.includes(subject)
  );

  // 회의 관련 단어가 있는지 확인
  const hasMeeting = meetingPatterns.some((meeting) =>
    lowerMessage.includes(meeting)
  );

  // 조회/확인 의도가 있는지 확인
  const queryPatterns = [
    "있어",
    "알려",
    "보여",
    "확인",
    "조회",
    "뭐있",
    "리스트",
    "목록",
    "현황",
  ];
  const hasQuery = queryPatterns.some((query) => lowerMessage.includes(query));

  return hasSubject && hasMeeting && hasQuery;
}

// [내회의관련-추가] Gemini AI를 활용한 정확한 의도 분류 함수
async function getGeminiIntentClassification(message: string): Promise<{
  isMyMeetingRequest: boolean;
  isAvailabilityCheck: boolean;
  isRecommendationRequest: boolean;
  isReservationRequest: boolean;
  needsSubjectConfirmation: boolean;
  confidence: number;
  reasoning: string;
}> {
  const prompt = `당신은 한국어 회의실 예약 봇의 자연어 분류 전문가입니다. 사용자 메시지를 4가지 의도로 정확히 분류하세요.

**분류 기준:**

1. **내 회의 조회** - 사용자 본인의 예약/회의를 조회
   - 패턴: "내/나" + "회의/예약/미팅" + 조회의도
   - 예시: "오늘 나 회의 있어?", "내 예약 알려줘", "내가 참석하는 회의"

2. **전체 현황 조회** - 회의실 전반의 예약 현황 확인
   - 패턴: 주체 없이 "현황/상황/조회" 또는 "회의실"이 주체
   - 예시: "내일 회의실 현황", "The1 예약 상황"

3. **회의실 추천** - 빈 회의실이나 적합한 회의실 추천 요청
   - 패턴: "추천/가능/빈/비어"
   - 예시: "회의실 추천해줘", "빈 회의실 알려줘"

4. **직접 예약** - 특정 회의실/시간 예약 요청
   - 패턴: "예약해/잡아/예약하고 싶어"
   - 예시: "내일 2시 The1 예약해줘"

**애매한 경우:** 주체가 불분명하면 needsSubjectConfirmation: true
- "내일 예약 알려줘", "오늘 회의 있어?", "예약 확인해줘"

메시지: "${message}"

JSON으로만 응답:
{
  "isMyMeetingRequest": boolean,
  "isAvailabilityCheck": boolean,
  "isRecommendationRequest": boolean,
  "isReservationRequest": boolean,
  "needsSubjectConfirmation": boolean,
  "confidence": number,
  "reasoning": "분류 근거"
}`;

  try {
    const response = await fetch(
      "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=" +
        process.env.GEMINI_API_KEY,
      {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          contents: [{ parts: [{ text: prompt }] }],
          generationConfig: {
            temperature: 0.1, // 낮은 온도로 일관성 확보
            topK: 1,
            topP: 0.8,
            maxOutputTokens: 300,
          },
        }),
      }
    );

    if (!response.ok) {
      throw new Error("Gemini API 호출 실패");
    }

    const data = await response.json();
    if (data.candidates && data.candidates[0] && data.candidates[0].content) {
      const text = data.candidates[0].content.parts[0].text.trim();

      // [내회의관련-수정] JSON 파싱 시도 (코드블록 제거 포함)
      try {
        // 코드블록 마크다운 제거
        let cleanText = text
          .replace(/```json\s*/g, "")
          .replace(/```\s*/g, "")
          .trim();

        const result = JSON.parse(cleanText);
        return {
          isMyMeetingRequest: result.isMyMeetingRequest || false,
          isAvailabilityCheck: result.isAvailabilityCheck || false,
          isRecommendationRequest: result.isRecommendationRequest || false,
          isReservationRequest: result.isReservationRequest || false,
          needsSubjectConfirmation: result.needsSubjectConfirmation || false,
          confidence: result.confidence || 0.5,
          reasoning: result.reasoning || "분류 실패",
        };
      } catch (parseError) {
        console.error("Gemini 응답 JSON 파싱 실패:", text);
        throw new Error("AI 응답 파싱 실패");
      }
    }

    throw new Error("Gemini 응답 형식 오류");
  } catch (error) {
    console.error("Gemini 의도 분류 실패:", error);
    // [내회의관련-수정] 폴백: 패턴 기반 분류 (기존 기능 보호)
    const isMyMeeting = hasMyMeetingPattern(message);
    const hasReservationAction = /예약해|잡아|예약하고\s*싶어/.test(message);
    const hasRecommendation = /추천|가능|빈|비어/.test(message);
    const hasStatusCheck = /현황|상황|조회/.test(message);

    // [내회의관련-수정] "회의실 현황" 같은 명확한 전체 현황 요청은 주체 확인 불필요
    const isExplicitRoomStatus = /회의실\s*(현황|상황|상태)/.test(message);

    return {
      isMyMeetingRequest: isMyMeeting,
      isAvailabilityCheck: hasStatusCheck && !isMyMeeting,
      isRecommendationRequest: hasRecommendation,
      isReservationRequest: hasReservationAction,
      needsSubjectConfirmation:
        !isMyMeeting &&
        !hasReservationAction &&
        !hasRecommendation &&
        hasStatusCheck &&
        !isExplicitRoomStatus, // [내회의관련-추가] 명확한 회의실 현황 요청은 제외
      confidence: 0.4,
      reasoning: "AI 분류 실패로 패턴 기반 폴백 사용",
    };
  }
}

// 복잡한 표현 파싱을 위한 강화된 폴백 함수
function parseComplexExpressionFallback(message: string): ParseResult {
  const result: ParseResult = {
    date: null,
    time: null,
    room: null,
    isAvailabilityCheck: false,
    isReservationRequest: false,
  };

  // 참석 인원수 파싱
  const attendeePatterns = [
    /(\d+)명/, // "4명"
    /(\d+)인/, // "4인"
    /(\d+)명이/, // "4명이"
    /(\d+)명의/, // "4명의"
    /(\d+)명에서/, // "4명에서"
    /(\d+)명 정도/, // "4명 정도"
    /(\d+)명 회의/, // "4명 회의"
    /(\d+)명으로/, // "4명으로"
    /(\d+)명이서/, // "4명이서"
  ];

  for (const pattern of attendeePatterns) {
    const match = message.match(pattern);
    if (match) {
      const count = parseInt(match[1]);
      if (count > 0 && count <= 50) {
        // 합리적인 범위 내에서만
        result.attendeeCount = count;
        console.log(`📊 참석 인원 감지: ${count}명`);
        break;
      }
    }
  }

  // 지속시간 파싱 추가 (한글 숫자 포함)
  const durationPatterns = [
    /(\d+)시간(\d+)분/, // "2시간30분" - 먼저 체크
    /(\d+)시간\s*(\d+)분/, // "2시간 30분" (공백 포함)
    /(\d+)시간/, // "2시간"
    /(\d+)분/, // "90분"
    /(삼십|30)분/, // "삼십분" 또는 "30분"
    /(한|1)시간/, // "한시간" 또는 "1시간"
    /(두|2)시간/, // "두시간" 또는 "2시간"
    /(세|3)시간/, // "세시간" 또는 "3시간"
  ];

  // 한글 숫자 변환 함수
  function convertKoreanNumber(text: string): number {
    const koreanNumbers: { [key: string]: number } = {
      한: 1,
      하나: 1,
      일: 1,
      두: 2,
      둘: 2,
      이: 2,
      세: 3,
      셋: 3,
      삼: 3,
      네: 4,
      넷: 4,
      사: 4,
      다섯: 5,
      오: 5,
      여섯: 6,
      육: 6,
      일곱: 7,
      칠: 7,
      여덟: 8,
      팔: 8,
      아홉: 9,
      구: 9,
      열: 10,
      십: 10,
      스무: 20,
      이십: 20,
      서른: 30,
      삼십: 30,
    };
    return koreanNumbers[text] || parseInt(text) || 0;
  }

  for (const pattern of durationPatterns) {
    const match = message.match(pattern);
    if (match) {
      let durationMinutes = 0;

      if (pattern.source.includes("시간") && pattern.source.includes("분")) {
        // "2시간30분" 또는 "2시간 30분" 형태
        const hours = convertKoreanNumber(match[1]);
        const minutes = convertKoreanNumber(match[2]);
        durationMinutes = hours * 60 + minutes;
      } else if (pattern.source.includes("시간")) {
        // "2시간" 또는 "한시간" 형태
        const hours = convertKoreanNumber(match[1]);
        durationMinutes = hours * 60;
      } else if (pattern.source.includes("분")) {
        // "90분" 또는 "삼십분" 형태
        const minutes = convertKoreanNumber(match[1]);
        durationMinutes = minutes;
      }

      if (durationMinutes > 0 && durationMinutes <= 480) {
        // 최대 8시간
        result.duration = durationMinutes;
        console.log(`⏱️ 지속시간 감지: ${durationMinutes}분`);
        break;
      }
    }
  }

  // 날짜 파싱
  const today = new Date();
  const currentYear = today.getFullYear();
  const currentMonth = today.getMonth();
  const currentDate = today.getDate();
  const currentDay = today.getDay(); // 0: 일요일, 1: 월요일, ..., 6: 토요일

  // "다음 주" 패턴
  if (
    message.includes("다음 주") ||
    message.includes("다음주") ||
    message.includes("차주")
  ) {
    const dayMap: { [key: string]: number } = {
      월요일: 1,
      화요일: 2,
      수요일: 3,
      목요일: 4,
      금요일: 5,
      토요일: 6,
      일요일: 0,
    };

    for (const [dayName, dayNum] of Object.entries(dayMap)) {
      if (message.includes(dayName)) {
        // 다음 주 해당 요일 계산
        const daysUntilNextWeek = 7 - currentDay; // 다음 주 일요일까지 남은 일수
        const daysFromSundayToTarget = dayNum; // 일요일부터 목표 요일까지 일수
        const daysToAdd = daysUntilNextWeek + daysFromSundayToTarget;

        const targetDate = new Date(today);
        targetDate.setDate(currentDate + daysToAdd);
        result.date = targetDate.toISOString().split("T")[0];
        break;
      }
    }
  }

  // "이번 주" 패턴
  else if (message.includes("이번 주") || message.includes("이번주")) {
    const dayMap: { [key: string]: number } = {
      월요일: 1,
      화요일: 2,
      수요일: 3,
      목요일: 4,
      금요일: 5,
      토요일: 6,
      일요일: 0,
    };

    for (const [dayName, dayNum] of Object.entries(dayMap)) {
      if (message.includes(dayName)) {
        const daysToAdd = dayNum - currentDay;
        const targetDate = new Date(today);
        targetDate.setDate(currentDate + daysToAdd);
        result.date = targetDate.toISOString().split("T")[0];
        break;
      }
    }
  }

  // "내일" 패턴
  else if (message.includes("내일")) {
    const tomorrow = new Date(today);
    tomorrow.setDate(currentDate + 1);
    result.date = tomorrow.toISOString().split("T")[0];
  }

  // "오늘" 패턴
  else if (message.includes("오늘")) {
    result.date = today.toISOString().split("T")[0];
  }

  // 시간 파싱 - 오후/오전 + 시간 조합 먼저 처리
  const timeWithPeriodMatch = message.match(/(오전|오후)\s*(\d{1,2})시/);
  if (timeWithPeriodMatch) {
    const period = timeWithPeriodMatch[1];
    const hour = parseInt(timeWithPeriodMatch[2]);

    if (period === "오전") {
      if (hour >= 1 && hour <= 12) {
        const adjustedHour = hour === 12 ? 0 : hour; // 오전 12시는 00시
        result.time = `${adjustedHour.toString().padStart(2, "0")}:00`;
      }
    } else if (period === "오후") {
      if (hour >= 1 && hour <= 12) {
        const adjustedHour = hour === 12 ? 12 : hour + 12; // 오후 12시는 12시, 나머지는 +12
        result.time = `${adjustedHour.toString().padStart(2, "0")}:00`;
      }
    }
  }
  // 일반적인 시간 패턴 (오전/오후 없이)
  else if (message.includes("오후 늦게") || message.includes("오후늦게")) {
    result.time = "17:00";
  } else if (message.includes("오전 늦게") || message.includes("오전늦게")) {
    result.time = "11:00";
  } else if (message.includes("저녁")) {
    result.time = "18:00";
  } else if (message.includes("점심")) {
    result.time = "12:00";
  } else if (message.includes("오전")) {
    result.time = "09:00";
  } else if (message.includes("오후")) {
    result.time = "14:00";
  } else {
    // 시간 정확한 패턴 (예: "3시", "15시") - 오후/오전 조합이 없을 때만
    const timeMatch = message.match(/(\d{1,2})시/);
    if (timeMatch) {
      const hour = parseInt(timeMatch[1]);
      if (hour >= 0 && hour <= 23) {
        // 단순 숫자만 있는 경우 오후로 해석 (1~7시는 오후, 8~12시는 오전, 13~23시는 그대로)
        if (hour >= 1 && hour <= 7) {
          result.time = `${(hour + 12).toString().padStart(2, "0")}:00`; // 1~7시 → 13~19시 (오후)
        } else if (hour >= 8 && hour <= 12) {
          result.time = `${hour.toString().padStart(2, "0")}:00`; // 8~12시 → 그대로 (오전)
        } else {
          result.time = `${hour.toString().padStart(2, "0")}:00`; // 13~23시 → 그대로
        }
      }
    }
  }

  // 회의실 파싱 - findRoomName 함수 사용
  const roomResult = findRoomName(message);
  result.room = roomResult.roomName || null;
  result.needsRoomSelection = roomResult.needsSelection;
  result.possibleRooms = roomResult.possibleRooms;

  // 회의실 정보 요청 감지
  const roomInfoKeywords = [
    "수용",
    "인원",
    "정보",
    "몇 명",
    "몇명",
    "최대",
    "용량",
    "capacity",
    "회의실 정보",
    "각 회의실",
    "전체 회의실",
    "모든 회의실",
  ];

  const hasRoomInfoKeyword = roomInfoKeywords.some((keyword) =>
    message.toLowerCase().includes(keyword.toLowerCase())
  );

  if (hasRoomInfoKeyword) {
    // "각 회의실 수용가능한 인원", "회의실 정보", "모든 회의실 인원" 등
    result.isRoomInfoRequest = true;
    console.log("🏢 회의실 정보 요청 감지");
  }

  // 의도 파악
  const availabilityKeywords = [
    "가능",
    "비어",
    "빈",
    "사용 가능",
    "이용 가능",
    "예약 가능",
    "available",
    "free",
  ];

  const reservationKeywords = [
    "예약",
    "잡아",
    "예약해",
    "잡아줘",
    "reserve",
    "book",
  ];

  // 회의실 정보 요청이 아닌 경우에만 다른 의도 파악
  if (!result.isRoomInfoRequest) {
    if (
      availabilityKeywords.some((keyword) => message.includes(keyword)) ||
      hasStatusKeyword(message)
    ) {
      result.isAvailabilityCheck = true;
    }
    if (reservationKeywords.some((keyword) => message.includes(keyword))) {
      result.isReservationRequest = true;
    }
  }

  const greetingKeywords = [
    "안녕",
    "반가워",
    "하이",
    "hello",
    "hi",
    "ㅎㅇ",
    "누구야",
    "정체",
    "소개",
    "봇이야",
    "뭐해",
    "고마워",
    "감사",
    "이용 방법",
    "예약 방법",
    "어떻게 써",
    "사용법",
    "help",
    "도움말",
    "예약하는 법",
    "예약 절차",
    "예약 프로세스",
  ];
  if (greetingKeywords.some((kw) => message.includes(kw))) {
    result.isGreetingOrChitchat = true;
  }

  return result;
}

async function parseComplexMessage(message: string): Promise<ParseResult> {
  try {
    console.log("🚀: 질문: ", message);

    // 한국시간(KST) 기준 현재 날짜 계산
    const now = new Date();
    const koreaTime = new Date(now.getTime() + 9 * 60 * 60 * 1000); // UTC+9
    const today = koreaTime.toISOString().split("T")[0];
    const todayDate = new Date(today);
    const currentDay = todayDate.getDay(); // 0=일요일, 1=월요일, ..., 6=토요일

    // 한국어 요일명
    const dayNames = [
      "일요일",
      "월요일",
      "화요일",
      "수요일",
      "목요일",
      "금요일",
      "토요일",
    ];
    const todayDayName = dayNames[currentDay];

    // 날짜 계산 함수들
    const getDateOffset = (days: number) => {
      const targetDate = new Date(todayDate);
      targetDate.setDate(todayDate.getDate() + days);
      return targetDate.toISOString().split("T")[0];
    };

    const getNextWeekDay = (targetDayIndex: number) => {
      // 🔧 다음주는 무조건 7일 후부터 시작
      // 다음주 해당 요일 = 7일 후 + (목표요일 - 현재요일)의 차이
      const dayDifference = targetDayIndex - currentDay;
      const nextWeekOffset = 7 + dayDifference;
      console.log(
        `🔍 getNextWeekDay(${targetDayIndex}): currentDay=${currentDay}, dayDifference=${dayDifference}, nextWeekOffset=${nextWeekOffset}`
      );
      return getDateOffset(nextWeekOffset);
    };

    const getThisWeekDay = (targetDayIndex: number) => {
      const daysUntil = (targetDayIndex - currentDay + 7) % 7;
      return getDateOffset(daysUntil === 0 ? 0 : daysUntil);
    };

    const getWeekAfterNextDay = (targetDayIndex: number) => {
      // 🔧 다다음주는 무조건 14일 후부터 시작
      // 다다음주 해당 요일 = 14일 후 + (목표요일 - 현재요일)의 차이
      const dayDifference = targetDayIndex - currentDay;
      const weekAfterNextOffset = 14 + dayDifference;
      return getDateOffset(weekAfterNextOffset);
    };

    // 디버깅: AI에게 전달되는 날짜 값들 출력
    console.log("🔍 AI에게 전달되는 날짜 정보:");
    console.log("- 오늘:", today, "(", todayDayName, ")");
    console.log("- 다음주 월요일:", getNextWeekDay(1));
    console.log("- 다음주 화요일:", getNextWeekDay(2));
    console.log("- 다음주 수요일:", getNextWeekDay(3));

    // 회의실 매핑 정보를 문자열로 변환
    const roomMappingsText = Object.entries(ROOM_NAME_MAPPINGS)
      .map(
        ([officialName, aliases]) =>
          `- "${officialName}": ${aliases.map((alias) => `"${alias}"`).join(", ")}`
      )
      .join("\n");

    const response = await fetch(
      "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=" +
        process.env.GEMINI_API_KEY,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          contents: [
            {
              parts: [
                {
                  text: `한국어 회의실 예약 메시지 분석. 오늘: ${today} (${todayDayName})

메시지: "${message}"

회의실 매핑:
${roomMappingsText}

중요한 Focus Room 규칙:
- "포커스", "focus", "집중", "포커스 룸", "포커스룸" 등의 키워드가 명시적으로 언급된 경우에만 Focus Room 관련 처리
- 포커스룸 키워드가 언급되고 "east/이스트/동쪽/동편" 또는 "west/웨스트/서쪽/서편"이 명시되지 않은 경우에만 needsRoomSelection: true
- 포커스룸 키워드가 전혀 언급되지 않은 경우 needsRoomSelection: false, possibleRooms: null
- East, West, 웨스트, 이스트가 명확히 언급된 경우에만 해당 Focus Room을 지정
- "포커스 룸"(공백 있음)과 "포커스룸"(공백 없음) 모두 동일하게 처리
- 단순히 "회의실 추천", "회의실 예약" 등의 일반적인 요청에는 needsRoomSelection: false

날짜 규칙:
- 오늘=${today}, 내일=${getDateOffset(1)}, 모레=${getDateOffset(2)}
- 이번주: 월=${getThisWeekDay(1)}, 화=${getThisWeekDay(2)}, 수=${getThisWeekDay(3)}, 목=${getThisWeekDay(4)}, 금=${getThisWeekDay(5)}
- 다음주: 월=${getNextWeekDay(1)}, 화=${getNextWeekDay(2)}, 수=${getNextWeekDay(3)}, 목=${getNextWeekDay(4)}, 금=${getNextWeekDay(5)}
- 다다음주: 월=${getWeekAfterNextDay(1)}, 화=${getWeekAfterNextDay(2)}, 수=${getWeekAfterNextDay(3)}, 목=${getWeekAfterNextDay(4)}, 금=${getWeekAfterNextDay(5)}
- 숫자 날짜 형식: "7-24", "7/24", "07-24", "07/24" → 2025-07-24, "8-15", "8/15" → 2025-08-15
- 월일 형식: "7월 24일", "8월 15일" → 각각 2025-07-24, 2025-08-15

시간 규칙:
- 오후/오전 + 시간 조합: "오후 2시"=14:00, "오전 10시"=10:00, "오후 12시"=12:00, "오전 12시"=00:00
- 일반 표현: 오후늦게=17:00, 오전늦게=11:00, 저녁=18:00, 점심=12:00, 오전=09:00, 오후=14:00
- 단순 시간: 1~7시 → 13~19:00 (오후), 8~12시 → 08~12:00 (오전), 13~23시 → 그대로
- "오후 1시"~"오후 11시" → 13:00~23:00, "오전 1시"~"오전 11시" → 01:00~11:00

참석 인원 규칙:
- "4명", "5인", "6명 회의", "10명 정도" 등의 표현에서 숫자 추출
- 1~25명 범위 내에서만 유효

지속시간 규칙:
- "2시간", "3시간", "90분", "2시간30분", "1시간 30분" 등의 표현에서 분 단위로 추출
- 30분~480분(8시간) 범위 내에서만 유효
- 시간이 명시되지 않으면 null

의도 분류:
- availability: 예약 가능 여부 확인, 빈 회의실 조회, 추천 요청
- reservation: 예약 진행 요청
- roomInfo: 회의실 정보, 수용 인원, 전체 회의실 목록 요청

응답 형식:
{"intent": "availability|reservation|roomInfo", "roomName": "정확한회의실명|null", "date": "YYYY-MM-DD", "time": "HH:mm", "attendeeCount": 숫자|null, "duration": 분단위숫자|null, "needsRoomSelection": true|false, "possibleRooms": ["Focus Room (East)", "Focus Room (West)"]|null, "isComplex": true}

예시:
- "포커스룸 예약 가능?" → {"intent": "availability", "roomName": null, "date": "${today}", "time": null, "duration": null, "needsRoomSelection": true, "possibleRooms": ["Focus Room (East)", "Focus Room (West)"], "isComplex": true}
- "내일 오후 2시 포커스룸 2시간 예약해줘" → {"intent": "reservation", "roomName": null, "date": "${getDateOffset(1)}", "time": "14:00", "duration": 120, "needsRoomSelection": true, "possibleRooms": ["Focus Room (East)", "Focus Room (West)"], "isComplex": true}
- "4명이 회의를 할건데 오후 2시에 가능한 회의실 있을까?" → {"intent": "availability", "roomName": null, "date": "${today}", "time": "14:00", "attendeeCount": 4, "duration": null, "needsRoomSelection": false, "possibleRooms": null, "isComplex": true}
- "금요일 회의실 추천해줘" → {"intent": "availability", "roomName": null, "date": "${getThisWeekDay(5)}", "time": null, "attendeeCount": null, "duration": null, "needsRoomSelection": false, "possibleRooms": null, "isComplex": true}
- "내일 회의실 예약하고 싶어" → {"intent": "availability", "roomName": null, "date": "${getDateOffset(1)}", "time": null, "attendeeCount": null, "duration": null, "needsRoomSelection": false, "possibleRooms": null, "isComplex": true}
- "다음주 화요일 오후늦게 더원 3시간 잡아줘" → {"intent": "reservation", "roomName": "The1", "date": "${getNextWeekDay(2)}", "time": "17:00", "duration": 180, "needsRoomSelection": false, "possibleRooms": null, "isComplex": true}
- "오늘 라이트룸 1시부터 2시간 예약해줘" → {"intent": "reservation", "roomName": "Light Room", "date": "${today}", "time": "13:00", "duration": 120, "needsRoomSelection": false, "possibleRooms": null, "isComplex": true}
- "7-24 더1 오후2시 2시간 예약" → {"intent": "reservation", "roomName": "The1", "date": "2025-07-24", "time": "14:00", "duration": 120, "needsRoomSelection": false, "possibleRooms": null, "isComplex": true}
- "7/24 더1 오후2시 2시간 예약" → {"intent": "reservation", "roomName": "The1", "date": "2025-07-24", "time": "14:00", "duration": 120, "needsRoomSelection": false, "possibleRooms": null, "isComplex": true}
- "각 회의실 수용가능한 인원 알려줘" → {"intent": "roomInfo", "roomName": null, "date": null, "time": null, "attendeeCount": null, "duration": null, "needsRoomSelection": false, "possibleRooms": null, "isComplex": true}
- "회의실 정보" → {"intent": "roomInfo", "roomName": null, "date": null, "time": null, "attendeeCount": null, "duration": null, "needsRoomSelection": false, "possibleRooms": null, "isComplex": true}

JSON만 응답:`,
                },
              ],
            },
          ],
          generationConfig: {
            temperature: 0.1,
            topK: 1,
            topP: 0.1,
            maxOutputTokens: 1024,
          },
        }),
      }
    );

    if (!response.ok) {
      console.log(
        "❌ Gemini API 요청 실패:",
        response.status,
        response.statusText
      );
      throw new Error(`Gemini API 오류: ${response.status}`);
    }

    const data = await response.json();
    console.log(
      "✅ gemini-2.0-flash 응답 성공:",
      JSON.stringify(data, null, 2)
    );

    if (data.candidates && data.candidates[0] && data.candidates[0].content) {
      const text = data.candidates[0].content.parts[0].text;
      const jsonMatch = text.match(/\{[\s\S]*\}/);

      if (jsonMatch) {
        const geminiResult = JSON.parse(jsonMatch[0]);
        console.log("🎯 Gemini 파싱 결과:", geminiResult);

        // Gemini 응답을 ParseResult 형식으로 변환
        const result: ParseResult = {
          date: geminiResult.date || null,
          time: geminiResult.time || null,
          room: geminiResult.roomName || null, // roomName -> room 매핑
          isAvailabilityCheck: geminiResult.intent === "availability",
          isReservationRequest: geminiResult.intent === "reservation",
          isRoomInfoRequest: geminiResult.intent === "roomInfo", // 회의실 정보 요청 처리
          attendeeCount: geminiResult.attendeeCount || undefined,
          duration: geminiResult.duration || undefined, // 지속시간 추가
          needsRoomSelection: geminiResult.needsRoomSelection,
          possibleRooms: geminiResult.possibleRooms,
          isGreetingOrChitchat: geminiResult.isGreetingOrChitchat,
        };

        console.log("✅ ParseResult로 변환된 결과:", result);
        return result;
      }
    }

    throw new Error("Gemini 응답에서 JSON을 찾을 수 없음");
  } catch (error) {
    console.log("❌ Gemini API 오류:", error);
    console.log("🔄 강화된 규칙 기반 파싱으로 전환");
    return parseComplexExpressionFallback(message);
  }
}

// Gemini를 활용한 친절한 상담 직원 톤의 인사/잡담/이용법/예약법 답변 생성 함수
async function getGeminiChitchatReply(message: string): Promise<string> {
  const prompt = `너는 슬랙에서 동작하는 회의실 예약 봇이야. 사용자가 인사, 자기소개, 잡담, 감사, 봇에 대한 질문(예: '안녕', '누구야', '고마워', '뭐해', '소개', '정체', '봇이야', '이용 방법', '예약 방법', '어떻게 써?', '무엇을 할 수 있어?', 'help', '도움말', '예약하는 법', '예약 절차', '예약 프로세스')을 하면 친절한 상담 직원처럼 자연스럽고 따뜻하게 답변해줘.\n- 봇의 역할, 할 수 있는 일, 이용 방법, 한계(예: 예약 관련 질문만 답변 가능)를 구체적으로 설명해.\n- 만약 이용 방법, 예약 방법, 예약 절차, 예약 프로세스 등 사용법을 물으면 실제 예약 방법을 예시와 함께 상세하게 안내해. (예: '회의실 예약을 원하시면 원하는 날짜, 시간, 회의실명을 입력해 주세요. 예: 내일 오후 2시 The1 예약해줘, 12월 25일 오전 10시 30분 Light Room 예약, 다음주 월요일 1시 30분 Focus Room (East) 예약')\n- 회의실 이름은 반드시 아래 중 하나만 사용: The1, The2, The3, The4, Light Room, Dark Room, Focus Room (East), Focus Room (West)\n- 예시 외의 회의실명(예: 아크, 넥스트 등)은 절대 안내하지 마.\n- 예시: '저는 pxd 회의실 예약을 도와드리는 슬랙 봇이에요. 회의실 예약, 추천, 현황 안내가 필요하시면 말씀해 주세요!'\n- 예약/추천/현황/정보 요청이 아니면 예약 안내는 하지 말고, 봇의 역할과 도움을 줄 수 있다는 점을 간단히 언급해.\n\n사용자 메시지: "${message}"`;
  try {
    const response = await fetch(
      "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=" +
        process.env.GEMINI_API_KEY,
      {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          contents: [{ parts: [{ text: prompt }] }],
          generationConfig: {
            temperature: 0.85,
            topK: 8,
            topP: 0.9,
            maxOutputTokens: 500,
          },
        }),
      }
    );
    if (!response.ok)
      return "안녕하세요! 저는 pxd 회의실 예약을 도와드리는 슬랙 봇입니다.";
    const data = await response.json();
    if (data.candidates && data.candidates[0] && data.candidates[0].content) {
      const text = data.candidates[0].content.parts[0].text;
      return text.trim();
    }
    return "안녕하세요! 저는 pxd 회의실 예약을 도와드리는 슬랙 봇입니다.";
  } catch (e) {
    return "안녕하세요! 저는 pxd 회의실 예약을 도와드리는 슬랙 봇입니다.";
  }
}

export async function parseMessage(message: string): Promise<ParseResult> {
  console.log("🚀 Google Gemini 2.0 Flash AI 파싱 시스템 시작:", message);

  // greetingKeywords(인사/이용법/잡담 등)에 해당하면 무조건 chitchat intent 우선 처리
  const greetingKeywords = [
    "안녕",
    "반가워",
    "하이",
    "hello",
    "hi",
    "ㅎㅇ",
    "누구야",
    "정체",
    "소개",
    "봇이야",
    "뭐해",
    "고마워",
    "감사",
    "이용 방법",
    "예약 방법",
    "어떻게 써",
    "사용법",
    "help",
    "도움말",
    "예약하는 법",
    "예약 절차",
    "예약 프로세스",
  ];
  if (greetingKeywords.some((kw) => message.includes(kw))) {
    const geminiReply = await getGeminiChitchatReply(message);
    return { isGreetingOrChitchat: true, geminiReply } as ParseResult;
  }

  // 🆕 고급 의도 분류 시스템 적용 (복잡한 표현 감지 전에 실행)
  console.log("🎯 Gemini 의도 분류 시작...");
  try {
    const intentClassification = await getGeminiIntentClassification(message);
    console.log("✅ 의도 분류 결과:", intentClassification);

    // 내 회의 조회 요청인 경우 특별 처리
    if (intentClassification.isMyMeetingRequest) {
      console.log("📋 내 회의 조회 요청 감지");
      return {
        date: null,
        time: null,
        room: null,
        isAvailabilityCheck: false,
        isReservationRequest: false,
        isMyMeetingRequest: true,
        confidence: intentClassification.confidence,
        reasoning: intentClassification.reasoning,
      };
    }

    // 주체 확인이 필요한 경우
    if (intentClassification.needsSubjectConfirmation) {
      console.log("❓ 주체 확인 필요한 요청 감지");
      return {
        date: null,
        time: null,
        room: null,
        isAvailabilityCheck: false,
        isReservationRequest: false,
        needsSubjectConfirmation: true,
        confidence: intentClassification.confidence,
        reasoning: intentClassification.reasoning,
      };
    }
  } catch (error) {
    console.log(
      "⚠️ 의도 분류 실패, 기존 파싱으로 진행:",
      error instanceof Error ? error.message : String(error)
    );
  }

  // 복잡한 표현 감지
  const hasComplexExpression =
    message.includes("다음 주") ||
    message.includes("이번 주") ||
    message.includes("오후 늦게") ||
    message.includes("오전 늦게") ||
    message.includes("저녁") ||
    message.includes("다다음 주") ||
    message.includes("차차주") ||
    message.includes("예약") ||
    message.includes("확인") ||
    message.includes("추천") ||
    message.includes("보여") ||
    message.includes("찾아") ||
    message.length > 15; // 15글자 이상이면 복잡한 표현으로 간주

  if (hasComplexExpression) {
    try {
      // Google Gemini 2.0 Flash AI 파싱 시도
      console.log("🎯 Gemini AI 파싱 시작...");
      const aiResult = await parseComplexMessage(message);
      console.log("✅ Gemini AI 파싱 성공:", aiResult);

      // AI 결과 검증: "다음주" 메시지에 대한 날짜 검증
      if (message.includes("다음주") || message.includes("다음 주")) {
        // 규칙 기반으로 다음주 월요일 계산
        const today = new Date();
        const currentDay = today.getDay();
        const daysUntilNextWeek = 7 - currentDay;
        const daysFromSundayToMonday = 1; // 월요일
        const totalDays = daysUntilNextWeek + daysFromSundayToMonday;
        const correctNextMonday = new Date(today);
        correctNextMonday.setDate(today.getDate() + totalDays);
        const expectedDate = correctNextMonday.toISOString().split("T")[0];

        console.log("🔍 AI 날짜 검증:");
        console.log("- AI 결과:", aiResult.date);
        console.log("- 예상 날짜:", expectedDate);

        // AI가 틀린 날짜를 반환했다면 규칙 기반 사용
        if (aiResult.date !== expectedDate && message.includes("월요일")) {
          console.log("❌ AI 날짜가 틀렸습니다. 규칙 기반 파싱 사용");
          const fallbackResult = parseComplexExpressionFallback(message);
          console.log("🔄 규칙 기반 파싱 결과:", fallbackResult);
          return fallbackResult;
        }
      }

      return aiResult;
    } catch (aiError) {
      console.log(
        "❌ Gemini AI 파싱 실패, 강화된 규칙 파싱으로 전환:",
        aiError
      );
      try {
        // 강화된 폴백 파싱 시도
        const fallbackResult = parseComplexExpressionFallback(message);
        console.log("🔄 강화된 규칙 파싱 결과:", fallbackResult);
        return fallbackResult;
      } catch (fallbackError) {
        console.warn(
          "⚠️ 강화된 규칙 파싱도 실패, 기본 규칙 사용:",
          fallbackError
        );
      }
    }
  } else {
    console.log("📝 단순 표현 감지, 기본 규칙 파싱 사용");
  }

  // 기본 규칙 기반 파싱 (기존 로직)
  const result: ParseResult = {
    date: null,
    time: null,
    room: null,
    isAvailabilityCheck: false,
    isReservationRequest: false,
  };

  // 참석 인원수 파싱 (기본 규칙)
  const attendeePatterns = [
    /(\d+)명/, // "4명"
    /(\d+)인/, // "4인"
    /(\d+)명이/, // "4명이"
    /(\d+)명의/, // "4명의"
    /(\d+)명에서/, // "4명에서"
    /(\d+)명 정도/, // "4명 정도"
    /(\d+)명 회의/, // "4명 회의"
    /(\d+)명으로/, // "4명으로"
    /(\d+)명이서/, // "4명이서"
  ];

  for (const pattern of attendeePatterns) {
    const match = message.match(pattern);
    if (match) {
      const count = parseInt(match[1]);
      if (count > 0 && count <= 50) {
        // 합리적인 범위 내에서만
        result.attendeeCount = count;
        console.log(`📊 참석 인원 감지 (기본 규칙): ${count}명`);
        break;
      }
    }
  }

  // 지속시간 파싱 (기본 규칙) - 한글 숫자 포함
  const durationPatterns = [
    /(\d+)시간(\d+)분/, // "2시간30분" - 먼저 체크
    /(\d+)시간\s*(\d+)분/, // "2시간 30분" (공백 포함)
    /(\d+)시간/, // "2시간"
    /(\d+)분/, // "90분"
    /(삼십|30)분/, // "삼십분" 또는 "30분"
    /(한|1)시간/, // "한시간" 또는 "1시간"
    /(두|2)시간/, // "두시간" 또는 "2시간"
    /(세|3)시간/, // "세시간" 또는 "3시간"
  ];

  // 한글 숫자 변환 함수 (기본 규칙용)
  function convertKoreanNumberBasic(text: string): number {
    const koreanNumbers: { [key: string]: number } = {
      한: 1,
      하나: 1,
      일: 1,
      두: 2,
      둘: 2,
      이: 2,
      세: 3,
      셋: 3,
      삼: 3,
      네: 4,
      넷: 4,
      사: 4,
      다섯: 5,
      오: 5,
      여섯: 6,
      육: 6,
      일곱: 7,
      칠: 7,
      여덟: 8,
      팔: 8,
      아홉: 9,
      구: 9,
      열: 10,
      십: 10,
      스무: 20,
      이십: 20,
      서른: 30,
      삼십: 30,
    };
    return koreanNumbers[text] || parseInt(text) || 0;
  }

  for (const pattern of durationPatterns) {
    const match = message.match(pattern);
    if (match) {
      let durationMinutes = 0;

      if (pattern.source.includes("시간") && pattern.source.includes("분")) {
        // "2시간30분" 또는 "2시간 30분" 형태
        const hours = convertKoreanNumberBasic(match[1]);
        const minutes = convertKoreanNumberBasic(match[2]);
        durationMinutes = hours * 60 + minutes;
      } else if (pattern.source.includes("시간")) {
        // "2시간" 또는 "한시간" 형태
        const hours = convertKoreanNumberBasic(match[1]);
        durationMinutes = hours * 60;
      } else if (pattern.source.includes("분")) {
        // "90분" 또는 "삼십분" 형태
        const minutes = convertKoreanNumberBasic(match[1]);
        durationMinutes = minutes;
      }

      if (durationMinutes > 0 && durationMinutes <= 480) {
        // 최대 8시간
        result.duration = durationMinutes;
        console.log(`⏱️ 지속시간 감지 (기본 규칙): ${durationMinutes}분`);
        break;
      }
    }
  }

  // 회의실 이름 파싱
  const roomResult = findRoomName(message);
  result.room = roomResult.roomName || null;
  result.needsRoomSelection = roomResult.needsSelection;
  result.possibleRooms = roomResult.possibleRooms;

  // 회의실 정보 요청 감지
  const roomInfoKeywords = [
    "수용",
    "인원",
    "정보",
    "몇 명",
    "몇명",
    "최대",
    "용량",
    "capacity",
    "회의실 정보",
    "각 회의실",
    "전체 회의실",
    "모든 회의실",
  ];

  const hasRoomInfoKeyword = roomInfoKeywords.some((keyword) =>
    message.toLowerCase().includes(keyword.toLowerCase())
  );

  if (hasRoomInfoKeyword) {
    // "각 회의실 수용가능한 인원", "회의실 정보", "모든 회의실 인원" 등
    result.isRoomInfoRequest = true;
    console.log("🏢 회의실 정보 요청 감지");
  }

  // 의도 파악
  const availabilityKeywords = [
    "가능",
    "비어",
    "빈",
    "사용 가능",
    "이용 가능",
    "예약 가능",
    "available",
    "free",
  ];

  const reservationKeywords = [
    "예약",
    "잡아",
    "예약해",
    "잡아줘",
    "reserve",
    "book",
  ];

  // 회의실 정보 요청이 아닌 경우에만 다른 의도 파악
  if (!result.isRoomInfoRequest) {
    if (
      availabilityKeywords.some((keyword) => message.includes(keyword)) ||
      hasStatusKeyword(message)
    ) {
      result.isAvailabilityCheck = true;
    }
    if (reservationKeywords.some((keyword) => message.includes(keyword))) {
      result.isReservationRequest = true;
    }
  }

  // 기본 룰 기반 파싱
  // 날짜 파싱 - 복잡한 표현부터 우선 처리
  const today = new Date();
  const currentDay = today.getDay(); // 0=일요일, 1=월요일, ...

  // 다다음 주 요일 계산
  if (message.includes("다다음") && message.includes("주")) {
    const dayNames = [
      "일요일",
      "월요일",
      "화요일",
      "수요일",
      "목요일",
      "금요일",
      "토요일",
    ];
    for (let i = 0; i < dayNames.length; i++) {
      if (message.includes(dayNames[i])) {
        const targetDay = i;
        // 다다음 주의 해당 요일까지 남은 일수 계산
        const daysUntilNextWeek = 7 - currentDay; // 다음 주 일요일까지 남은 일수
        const daysFromSundayToTarget = targetDay; // 일요일부터 목표 요일까지 일수
        const totalDays = daysUntilNextWeek + daysFromSundayToTarget + 7; // 다다음 주는 +7일 더

        const nextWeekDate = new Date(today);
        nextWeekDate.setDate(today.getDate() + totalDays);
        result.date = nextWeekDate.toISOString().split("T")[0];
        console.log(
          `다다음 주 ${dayNames[i]} 감지: ${nextWeekDate.toDateString()} (${result.date})`
        );
        break;
      }
    }
  }

  // 다음 주 요일 계산
  else if (message.includes("다음") && message.includes("주")) {
    const dayNames = [
      "일요일",
      "월요일",
      "화요일",
      "수요일",
      "목요일",
      "금요일",
      "토요일",
    ];
    for (let i = 0; i < dayNames.length; i++) {
      if (message.includes(dayNames[i])) {
        const targetDay = i;
        // 다음 주의 해당 요일까지 남은 일수 계산
        const daysUntilNextWeek = 7 - currentDay; // 다음 주 일요일까지 남은 일수
        const daysFromSundayToTarget = targetDay; // 일요일부터 목표 요일까지 일수
        const totalDays = daysUntilNextWeek + daysFromSundayToTarget;

        const nextWeekDate = new Date(today);
        nextWeekDate.setDate(today.getDate() + totalDays);
        result.date = nextWeekDate.toISOString().split("T")[0];
        console.log(
          `다음 주 ${dayNames[i]} 감지: ${nextWeekDate.toDateString()} (${result.date})`
        );
        break;
      }
    }
  }

  // 이번 주 요일 계산
  else if (message.includes("이번") && message.includes("주")) {
    const dayNames = [
      "일요일",
      "월요일",
      "화요일",
      "수요일",
      "목요일",
      "금요일",
      "토요일",
    ];
    for (let i = 0; i < dayNames.length; i++) {
      if (message.includes(dayNames[i])) {
        const targetDay = i;
        const daysUntil = (targetDay - currentDay + 7) % 7;
        const thisWeekDate = new Date(today);
        thisWeekDate.setDate(today.getDate() + daysUntil);
        result.date = thisWeekDate.toISOString().split("T")[0];
        break;
      }
    }
  }

  // 기본 날짜 표현
  else if (message.includes("내일")) {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    result.date = tomorrow.toISOString().split("T")[0];
  } else if (message.includes("오늘")) {
    result.date = today.toISOString().split("T")[0];
  } else {
    // 날짜 형식 매칭 (예: 5월 2일, 5/2, 05-02 등)
    const dateMatch = message.match(/(\d{1,2})[월/\-](\d{1,2})[일]?/);
    if (dateMatch) {
      const month = parseInt(dateMatch[1]);
      const day = parseInt(dateMatch[2]);
      if (month >= 1 && month <= 12 && day >= 1 && day <= 31) {
        const date = new Date();
        date.setMonth(month - 1, day);
        if (date < new Date()) {
          date.setFullYear(date.getFullYear() + 1);
        }
        result.date = date.toISOString().split("T")[0];
      }
    }
  }

  // 시간 파싱
  if (message.includes("오후") && message.includes("늦게")) {
    result.time = "17:00";
    console.log("오후 늦게 감지: 17시로 설정");
  } else if (message.includes("오전") && message.includes("늦게")) {
    result.time = "11:00";
  } else if (message.includes("저녁")) {
    result.time = "18:00";
  } else if (message.includes("아침") && message.includes("일찍")) {
    result.time = "09:00";
  } else if (message.includes("점심")) {
    result.time = "12:00";
  } else {
    // 구체적인 시간 파싱
    const timeMatch = message.match(/(\d{1,2})시/);
    if (timeMatch) {
      const hour = parseInt(timeMatch[1]);
      if (!isNaN(hour) && hour >= 0 && hour <= 24) {
        if (message.includes("오전")) {
          result.time =
            hour === 12 ? "00:00" : `${hour.toString().padStart(2, "0")}:00`;
        } else if (message.includes("오후")) {
          result.time =
            hour === 12
              ? "12:00"
              : `${(hour + 12).toString().padStart(2, "0")}:00`;
        } else {
          result.time = hour >= 1 && hour <= 9 ? `${hour + 12}시` : `${hour}시`;
        }
      }
    }
  }

  console.log("규칙 기반 파싱 결과:", {
    originalText: message,
    roomName: result.room,
    needsRoomSelection: result.needsRoomSelection,
    possibleRooms: result.possibleRooms,
    parsedTime: result.time,
    parsedDate: result.date,
    isAvailabilityCheck: result.isAvailabilityCheck,
    isReservationRequest: result.isReservationRequest,
    usedAI: false, // AI 사용하지 않음
  });

  // --- [추가] 현황/상황/상태/조회 키워드가 있으면 isAvailabilityCheck 보장 ---
  if (hasStatusKeyword(message)) {
    result.isAvailabilityCheck = true;
  }

  return result;
}

export function generateResponse(
  intent: MessageIntent,
  availableRooms: string[]
): string {
  const dateStr = intent.date
    ? format(intent.date, "M월 d일 (EEEE)", { locale: ko })
    : "오늘";

  const timeStr = intent.time ? ` ${intent.time}시` : "";

  if (intent.isAvailabilityCheck) {
    if (availableRooms.length === 0) {
      return `${dateStr}${timeStr}에는 예약 가능한 회의실이 없습니다. 다른 시간을 선택해주세요.`;
    }
    return `${dateStr}${timeStr}에 예약 가능한 회의실은 다음과 같습니다:\n${availableRooms.join(", ")}`;
  }

  if (intent.isReservationRequest) {
    return `${dateStr}${timeStr} 회의실 예약을 도와드리겠습니다. 예약하실 회의실을 선택해주세요:\n${availableRooms.join(", ")}`;
  }

  return `${dateStr} 회의실 예약 현황을 조회했습니다.`;
}

// --- [추가] 현황/상황/상태/조회 키워드로 예약 현황 조회 플래그 보장 ---
function hasStatusKeyword(message: string): boolean {
  return ["현황", "상황", "상태", "조회"].some((kw) => message.includes(kw));
}
