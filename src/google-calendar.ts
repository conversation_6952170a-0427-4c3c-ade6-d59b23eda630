import { google } from "googleapis";
import { OAuth2Client } from "google-auth-library";
import "dotenv/config";

const {
  GOOGLE_CLIENT_ID,
  GOOGLE_CLIENT_SECRET,
  GOOGLE_REDIRECT_URI,
  GOOGLE_REFRESH_TOKEN,
} = process.env;

// Google OAuth2 클라이언트 초기화
function getAuth() {
  if (
    !GOOGLE_CLIENT_ID ||
    !GOOGLE_CLIENT_SECRET ||
    !GOOGLE_REDIRECT_URI ||
    !GOOGLE_REFRESH_TOKEN
  ) {
    throw new Error("Missing required Google OAuth credentials");
  }

  const oAuth2Client = new OAuth2Client(
    GOOGLE_CLIENT_ID,
    GOOGLE_CLIENT_SECRET,
    GOOGLE_REDIRECT_URI
  );

  oAuth2Client.setCredentials({ refresh_token: GOOGLE_REFRESH_TOKEN });
  return oAuth2Client;
}

// 시간 포맷팅 함수
function formatTime(dt: string | null | undefined) {
  if (!dt) return "";
  try {
    const date = new Date(dt);
    const hh = String(date.getHours()).padStart(2, "0");
    const min = String(date.getMinutes()).padStart(2, "0");
    return `${hh}:${min}`;
  } catch {
    return String(dt);
  }
}

// 구글 캘린더에서 회의실 예약 정보 조회
export async function getRoomEvents(calendarId: string, targetDate?: string) {
  let dateObj: Date;
  if (targetDate) {
    // 한국 시간대로 올바르게 날짜를 파싱
    console.log(`📅 Processing target date: ${targetDate}`);
    dateObj = new Date(targetDate + "T00:00:00+09:00");
    if (isNaN(dateObj.getTime())) {
      console.log("❌ Invalid date format, using current date");
      dateObj = new Date();
    }
  } else {
    dateObj = new Date();
  }

  console.log(`📅 Final date object: ${dateObj.toISOString()}`);
  console.log(`📅 Local date string: ${dateObj.toLocaleDateString("ko-KR")}`);

  const start = new Date(dateObj);
  start.setHours(0, 0, 0, 0);
  const end = new Date(dateObj);
  end.setHours(23, 59, 59, 999);

  const timeMin = start.toISOString();
  const timeMax = end.toISOString();

  console.log(`🕐 Time range: ${timeMin} to ${timeMax}`);

  try {
    const auth = getAuth();
    const calendar = google.calendar({ version: "v3", auth });
    const res = await calendar.events.list({
      calendarId,
      timeMin,
      timeMax,
      maxResults: 10,
      singleEvents: true,
      orderBy: "startTime",
    });

    const events = res.data.items || [];
    console.log(`📝 Found ${events.length} events for ${targetDate}`);
    return events.map((event) => ({
      start: formatTime(event.start?.dateTime || event.start?.date),
      end: formatTime(event.end?.dateTime || event.end?.date),
      summary: event.summary || "제목 없음",
      attendees: (event.attendees || [])
        .filter(
          (a) => a.email && !a.email.includes("@resource.calendar.google.com")
        )
        .map((a) => ({
          email: a.email || "",
          displayName: a.displayName || "",
        }))
        .filter((attendee) => !!attendee.email),
    }));
  } catch (error) {
    console.error("Error fetching room events:", error);
    throw error;
  }
}

interface CalendarEvent {
  summary: string;
  description: string;
  location: string;
  start: Date;
  end: Date;
}

export async function createCalendarEvent(
  calendarId: string,
  event: CalendarEvent
) {
  try {
    const auth = getAuth();
    const calendar = google.calendar({ version: "v3", auth });

    const calendarEvent = {
      summary: event.summary,
      description: event.description,
      location: event.location,
      start: {
        dateTime: event.start.toISOString(),
        timeZone: "Asia/Seoul",
      },
      end: {
        dateTime: event.end.toISOString(),
        timeZone: "Asia/Seoul",
      },
    };

    const response = await calendar.events.insert({
      calendarId: calendarId,
      requestBody: calendarEvent,
    });

    console.log("Event created:", response.data);
    return response.data;
  } catch (error) {
    console.error("Error creating calendar event:", error);
    throw error;
  }
}
