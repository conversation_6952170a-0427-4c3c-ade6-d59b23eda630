import "dotenv/config";
import { App, LogLevel, BlockButtonAction } from "@slack/bolt";
import { WebClient } from "@slack/web-api";
import { getRoomEvents } from "./google-calendar";
import { ROOM_CALENDARS, buildRoomInfoBlocks } from "./meetingRooms";
import { parseMessage } from "./nlpHandler";
import { getRoomReservationBlockKit } from "./formatRoomEvents";
import {
  recommendAvailableRooms,
  buildRecommendationBlocks,
} from "./recommendRoom";
import { reservationStateManager } from "./reservationState";
import {
  parseTimeInput,
  parseDuration,
  isTimeInRange,
  addMinutesToTime,
} from "./utils/timeUtils";
import {
  validateDurationAndCreateErrorMessage,
  makeDurationText,
} from "./utils/reservationUtils";
import {
  createTimeInputPromptBlocks,
  createDurationSelectionBlocks,
  createConfirmationBlocks,
} from "./messageBlocks";
import {
  isRoomAvailable,
  getMaxAvailableTime,
  createDurationOptions,
  findAvailableTimeSlots,
} from "./roomAvailability";
import {
  makeCalendarUrl,
  makeReservationMessage,
  makeRoomIcon,
} from "./utils/reservationUtils";

const app = new App({
  token: process.env.SLACK_BOT_TOKEN,
  signingSecret: process.env.SLACK_SIGNING_SECRET,
  socketMode: true,
  appToken: process.env.SLACK_APP_TOKEN,
  logLevel: LogLevel.INFO,
});

// 🆕 전역 로딩 메시지 추적
const globalLoadingMessages = new Map<string, any>();

// 🆕 로딩 메시지 안전 삭제 함수
async function safeDeleteLoadingMessage(
  loadingMsg: { ts?: string; channel?: string } | null,
  client: WebClient
) {
  console.log("🔄 로딩 메시지 삭제 시도:", {
    hasLoadingMsg: !!loadingMsg,
    ts: loadingMsg?.ts,
    channel: loadingMsg?.channel,
  });

  if (!loadingMsg?.ts) {
    console.log("🔄 로딩 메시지 없음, 삭제 스킵");
    return;
  }

  try {
    console.log("🔄 로딩 메시지 삭제 API 호출 시작...");
    await client.chat.delete({
      channel: loadingMsg.channel!,
      ts: loadingMsg.ts,
    });
    console.log("✅ 로딩 메시지 삭제 성공:", loadingMsg.ts);
  } catch (error: any) {
    if (error.data?.error === "message_not_found") {
      console.log("🔄 로딩 메시지 이미 삭제됨:", loadingMsg.ts);
    } else {
      console.log("❌ 로딩 메시지 삭제 실패:", {
        ts: loadingMsg.ts,
        error: error.message,
        errorData: error.data,
      });
    }
  }
}

app.event("message", async ({ message, say, client }) => {
  try {
    // DM에서만 응답하도록 체크
    if (message.channel_type !== "im") {
      console.log(
        `채널에서의 메시지는 무시합니다. 채널 타입: ${message.channel_type}`
      );
      return;
    }

    if (
      message.subtype === undefined &&
      message.text &&
      message.user &&
      typeof message.text === "string"
    ) {
      console.log("🔥 ===== 새로운 메시지 처리 시작 =====");
      console.log("Received message:", message.text);
      console.log("Processing message:", {
        text: message.text,
        channel: message.channel,
        ts: message.ts,
        user: message.user,
      });
      const userId = message.user;
      const userInfo = await client.users.info({ user: userId });
      const userName =
        userInfo.user?.real_name || userInfo.user?.name || "Unknown";

      // 예약 상태 확인 (채널별로)
      const existingStateResult = reservationStateManager.getStateByChannel(
        message.channel
      );

      // 🆕 예약 관련 키워드가 포함된 경우 즉시 로딩 메시지 표시
      let loadingMsg: any = null;
      const reservationKeywords = [
        "예약",
        "추천",
        "현황",
        "상황",
        "조회",
        "가능",
        "빈",
        "비어",
      ];
      const hasReservationKeyword = reservationKeywords.some((keyword) =>
        message.text?.includes(keyword)
      );

      // 🆕 중복 로딩 메시지 방지: 기존 상태가 있거나 포커스룸 선택이 필요한 경우 로딩 메시지 생성하지 않음
      let willNeedFocusSelection = false;

      // 미리 파싱해서 포커스룸 선택이 필요한지 확인
      if (hasReservationKeyword && !existingStateResult) {
        // 간단한 사전 체크: "포커스룸"이 포함되고 방향이 명시되지 않은 경우
        const messageText = message.text.toLowerCase();
        if (
          messageText.includes("포커스룸") &&
          !messageText.includes("동쪽") &&
          !messageText.includes("동편") &&
          !messageText.includes("서쪽") &&
          !messageText.includes("서편") &&
          !messageText.includes("east") &&
          !messageText.includes("west")
        ) {
          willNeedFocusSelection = true;
        }
      }

      if (
        hasReservationKeyword &&
        !existingStateResult &&
        !willNeedFocusSelection
      ) {
        const loadingId = Date.now();
        console.log(`🔄 [MAIN] 로딩 메시지 생성 시작... ID: ${loadingId}`);
        loadingMsg = await say(
          `:loading2: 더제로 봇이 답변을 준비 중입니다...`
        );
        console.log("🔄 [MAIN] 로딩 메시지 생성 완료:", {
          loadingId,
          ts: loadingMsg?.ts,
          channel: loadingMsg?.channel,
          text: loadingMsg?.text,
        });
      } else {
        console.log("🔄 [MAIN] 로딩 메시지 생성 스킵:", {
          hasReservationKeyword,
          existingStateResult: !!existingStateResult,
          willNeedFocusSelection,
        });
      }

      // 일반 메시지 처리 (먼저 파싱해서 새로운 요청인지 확인)
      const parsedData = await parseMessage(message.text);
      console.log("Parsed data:", parsedData);

      // 🆕 사전 체크 결과로 AI 파싱 결과 수정
      if (willNeedFocusSelection) {
        console.log(
          "🔧 사전 체크 결과로 AI 파싱 결과 수정: 포커스룸 선택 필요"
        );
        parsedData.needsRoomSelection = true;
        parsedData.possibleRooms = ["Focus Room (East)", "Focus Room (West)"];
        parsedData.room = null; // 특정 룸 선택 제거
        console.log("🔧 수정된 파싱 데이터:", {
          needsRoomSelection: parsedData.needsRoomSelection,
          possibleRooms: parsedData.possibleRooms,
          room: parsedData.room,
        });
      }

      // 인사/잡담/봇소개 intent 감지 시 안내 메시지 분기
      if (parsedData.isGreetingOrChitchat) {
        // 🆕 로딩 메시지 삭제
        await safeDeleteLoadingMessage(loadingMsg, client);

        const reply =
          parsedData.geminiReply ||
          "안녕하세요, 저는 pxd 회의실 예약을 돕는 슬렉 봇입니다.";
        if (reply.length > 3000) {
          const blocks = [];
          for (let i = 0; i < reply.length; i += 3000) {
            blocks.push({
              type: "section",
              text: { type: "mrkdwn", text: reply.slice(i, i + 3000) },
            });
          }
          await say({ blocks });
        } else {
          await say({ text: reply });
        }
        return;
      }

      // 🆕 내 회의 조회 요청 처리
      if (parsedData.isMyMeetingRequest) {
        console.log("📋 내 회의 조회 요청 처리");

        // 🆕 로딩 메시지 삭제
        if (loadingMsg?.ts) {
          try {
            await client.chat.delete({
              channel: loadingMsg.channel!,
              ts: loadingMsg.ts,
            });
          } catch (error) {
            console.log("로딩 메시지 삭제 실패:", error);
          }
        }

        await say(
          `📋 **내 회의 조회 기능**\n\n` +
            `죄송합니다. 현재 개인 회의 조회 기능은 개발 중입니다.\n\n` +
            `💡 **대신 이렇게 이용해보세요:**\n` +
            `• "오늘 회의실 현황" - 전체 회의실 현황 확인\n` +
            `• "내일 The1 예약 상황" - 특정 회의실 현황 확인\n` +
            `• "회의실 정보" - 전체 회의실 정보 확인`
        );
        return;
      }

      // 🆕 주체 확인 필요한 요청 처리
      if (parsedData.needsSubjectConfirmation) {
        console.log("❓ 주체 확인 필요한 요청 처리");

        // 🆕 로딩 메시지 삭제
        if (loadingMsg?.ts) {
          try {
            await client.chat.delete({
              channel: loadingMsg.channel!,
              ts: loadingMsg.ts,
            });
          } catch (error) {
            console.log("로딩 메시지 삭제 실패:", error);
          }
        }

        await say(
          `❓ **요청 내용을 명확히 해주세요**\n\n` +
            `다음 중 어떤 것을 원하시나요?\n\n` +
            `1️⃣ **내 개인 예약 조회** → "내 회의 알려줘"\n` +
            `2️⃣ **전체 회의실 현황** → "회의실 현황 알려줘"\n` +
            `3️⃣ **특정 회의실 현황** → "The1 예약 상황 알려줘"\n\n` +
            `💡 더 구체적으로 말씀해주시면 정확히 도와드릴 수 있습니다!`
        );
        return;
      }

      // 회의실 정보 요청인 경우
      if (parsedData.isRoomInfoRequest) {
        console.log("🏢 회의실 정보 요청 처리");

        // 🆕 로딩 메시지 삭제
        if (loadingMsg?.ts) {
          try {
            await client.chat.delete({
              channel: loadingMsg.channel!,
              ts: loadingMsg.ts,
            });
          } catch (error) {
            console.log("로딩 메시지 삭제 실패:", error);
          }
        }

        const roomInfoBlocks = buildRoomInfoBlocks();

        await say({
          text: "전체 회의실 정보입니다.",
          blocks: roomInfoBlocks,
        });
        return;
      }

      // 새로운 예약/조회 요청인 경우 기존 상태를 삭제하고 새로 시작
      if (parsedData.isAvailabilityCheck || parsedData.isReservationRequest) {
        // 기존 예약 상태가 있으면 삭제
        if (existingStateResult) {
          const [stateId] = existingStateResult;
          reservationStateManager.deleteState(stateId);
          console.log("기존 예약 상태 삭제 후 새로운 요청 처리");
        }

        // === [여기서부터 바로 예약 확정 분기 추가] ===
        console.log("🔍 바로 예약 확정 분기 체크:", {
          isReservationRequest: parsedData.isReservationRequest,
          date: parsedData.date,
          room: parsedData.room,
          time: parsedData.time,
          needsRoomSelection: parsedData.needsRoomSelection,
          duration: parsedData.duration,
        });

        if (
          parsedData.isReservationRequest &&
          parsedData.date &&
          parsedData.room &&
          parsedData.time &&
          !parsedData.needsRoomSelection // 포커스룸 선택 필요 없는 경우만
        ) {
          console.log("✅ 바로 예약 확정 분기 진입!");

          // 1. 구글 캘린더에서 해당 회의실의 예약 현황 조회
          const calendarId = ROOM_CALENDARS[parsedData.room];
          const events = await getRoomEvents(calendarId, parsedData.date);

          console.log(`📅 ${parsedData.room} 회의실 이벤트 조회 결과:`, events);

          // 2. 요청한 시간에 예약 가능한지 확인 (분 단위까지 정확히)
          const [hour, minute] = parsedData.time.split(":").map(Number);
          const isAvailable = isRoomAvailable(
            events,
            hour,
            parsedData.room,
            minute
          );

          if (!isAvailable) {
            // 🆕 로딩 메시지 삭제
            if (loadingMsg?.ts) {
              try {
                await client.chat.delete({
                  channel: loadingMsg.channel!,
                  ts: loadingMsg.ts,
                });
              } catch (error) {
                console.log("로딩 메시지 삭제 실패:", error);
              }
            }

            await say({
              blocks: [
                {
                  type: "section",
                  text: {
                    type: "mrkdwn",
                    text: makeReservationMessage({
                      roomName: parsedData.room!,
                      userName: "사용자",
                      date: parsedData.date!,
                      startTime: parsedData.time!,
                      endTime: null,
                      durationMinutes: 60,
                      isUnavailable: true,
                    }),
                  },
                },
              ],
            });
            return;
          }

          // 정확한 예약 가능 시간 계산 (분 단위까지 정확히)
          const [reqHour, reqMin] = parsedData.time!.split(":").map(Number);
          const maxEndTime = getMaxAvailableTime(events, reqHour, reqMin);
          const maxAvailableMinutes =
            maxEndTime.hour * 60 + maxEndTime.minute - (reqHour * 60 + reqMin);

          console.log(`🔍 예약 가능 시간 계산:`, {
            requestedTime: `${reqHour}:${reqMin.toString().padStart(2, "0")}`,
            maxEndTime: `${maxEndTime.hour}:${maxEndTime.minute.toString().padStart(2, "0")}`,
            maxAvailableMinutes: maxAvailableMinutes,
          });

          // 예약 가능한 시간대 계산 (슬롯 정보는 나중에 사용)
          const availableSlots: { start: string; end: string }[] =
            findAvailableTimeSlots(events, parsedData.date);
          let slotForState = availableSlots.find((slot) => {
            const slotStart = slot.start.split(":").map(Number);
            const slotEnd = slot.end.split(":").map(Number);
            const startMinutes = slotStart[0] * 60 + slotStart[1];
            const endMinutes = slotEnd[0] * 60 + slotEnd[1];
            const reqMinutes = reqHour * 60 + reqMin;
            return reqMinutes >= startMinutes && reqMinutes < endMinutes;
          }) || { start: "09:00", end: "22:00" };

          // 지속시간 결정: 사용자가 명시한 시간이 있으면 사용, 없으면 기본값
          let requestedDuration = parsedData.duration || null;
          let finalDuration: number;

          if (requestedDuration) {
            console.log(`🎯 사용자가 지속시간 명시: ${requestedDuration}분`);

            // 다음 예약 시간 정보 추가
            const nextReservationTime = `${maxEndTime.hour}:${maxEndTime.minute.toString().padStart(2, "0")}`;

            // 지속시간 검증 및 에러 메시지 생성
            const validation = validateDurationAndCreateErrorMessage({
              requestedDuration,
              maxAvailableMinutes,
              roomName: parsedData.room!,
              date: parsedData.date!,
              startTime: parsedData.time!,
              nextReservationTime,
            });

            if (!validation.isValid) {
              console.log(
                `❌ 지속시간 초과 감지: 요청(${requestedDuration}분) > 가능(${maxAvailableMinutes}분)`
              );

              // 🆕 로딩 메시지 삭제
              if (loadingMsg?.ts) {
                try {
                  await client.chat.delete({
                    channel: loadingMsg.channel!,
                    ts: loadingMsg.ts,
                  });
                } catch (error) {
                  console.log("로딩 메시지 삭제 실패:", error);
                }
              }

              await say({
                blocks: [
                  {
                    type: "section",
                    text: {
                      type: "mrkdwn",
                      text: validation.errorMessage!,
                    },
                  },
                ],
              });
              return;
            }
            finalDuration = requestedDuration;
            console.log(`⏱️ 사용자 요청 지속시간 사용: ${finalDuration}분`);
          } else {
            // 기본 지속시간 로직
            finalDuration =
              maxAvailableMinutes === 30
                ? 30
                : maxAvailableMinutes > 30
                  ? 60
                  : 30;
            console.log(`⏱️ 기본 지속시간 사용: ${finalDuration}분`);
          }

          // 디버깅 로그 추가
          console.log("slotForState:", slotForState);
          console.log("parsedData.time:", parsedData.time);
          console.log("maxAvailableMinutes:", maxAvailableMinutes);
          console.log("finalDuration:", finalDuration);
          console.log(
            "addMinutesToTime:",
            addMinutesToTime(parsedData.time!, finalDuration)
          );

          const startDate = new Date(parsedData.date + "T" + parsedData.time);
          const endDate = new Date(startDate.getTime() + finalDuration * 60000);
          const userName =
            userInfo.user?.real_name || userInfo.user?.name || "사용자";
          const calendarUrl = makeCalendarUrl({
            roomName: parsedData.room,
            userName,
            startDate,
            endDate,
            calendarId,
          });

          // 🆕 로딩 메시지 삭제
          if (loadingMsg?.ts) {
            try {
              await client.chat.delete({
                channel: loadingMsg.channel!,
                ts: loadingMsg.ts,
              });
            } catch (error) {
              console.log("로딩 메시지 삭제 실패:", error);
            }
          }

          await say({
            blocks: [
              {
                type: "section",
                text: {
                  type: "mrkdwn",
                  text: makeReservationMessage({
                    roomName: parsedData.room,
                    userName,
                    date: parsedData.date,
                    startTime: parsedData.time,
                    endTime: addMinutesToTime(parsedData.time, finalDuration),
                    durationMinutes: finalDuration,
                  }),
                },
              },
              {
                type: "actions",
                elements: [
                  {
                    type: "button",
                    text: {
                      type: "plain_text",
                      text: "Google Calendar에서 예약하기",
                    },
                    style: "primary",
                    url: calendarUrl,
                  },
                  {
                    type: "button",
                    text: {
                      type: "plain_text",
                      text: "🕐 종료 시간 변경",
                    },
                    value: JSON.stringify({
                      room: parsedData.room,
                      date: parsedData.date,
                      selectedTime: parsedData.time,
                      userName: userName,
                      action: "modify_duration",
                    }),
                    action_id: "modify_meeting_duration",
                  },
                ],
              },
            ],
            text: "회의실 예약",
          });
          return;
        }
        // === [여기까지] ===

        // 🆕 예약 요청인 경우에만 [FOCUS] 로딩 메시지 표시
        let loadingMessage;
        if (parsedData.isReservationRequest) {
          try {
            const focusLoadingId = Date.now();
            console.log(
              `🔄 [FOCUS] 로딩 메시지 생성 시작... ID: ${focusLoadingId}`
            );
            loadingMessage = await say({
              text: `더제로 봇이 답변을 입력 중입니다...`,
              blocks: [
                {
                  type: "section",
                  text: {
                    type: "mrkdwn",
                    text: `:loading2: 더제로 봇이 답변을 입력 중입니다...`,
                  },
                },
              ],
            });
            console.log("🔄 [FOCUS] 로딩 메시지 생성 완료:", {
              focusLoadingId,
              ts: loadingMessage?.ts,
              channel: loadingMessage?.channel,
            });

            // 🆕 전역 맵에 저장 (채널별로)
            if (loadingMessage?.ts) {
              globalLoadingMessages.set(message.channel, loadingMessage);
              console.log("🔄 [FOCUS] 로딩 메시지 전역 저장:", message.channel);
            }
          } catch (loadingError) {
            console.error("Error posting loading message:", loadingError);
          }
        } else {
          console.log("🔄 [FOCUS] 로딩 메시지 생성 스킵: 예약 요청이 아님");
        }

        // 포커스룸 선택이 필요한 경우 먼저 처리
        if (parsedData.needsRoomSelection && parsedData.possibleRooms) {
          // 🆕 로딩 메시지 삭제
          if (loadingMsg?.ts) {
            try {
              await client.chat.delete({
                channel: loadingMsg.channel!,
                ts: loadingMsg.ts,
              });
            } catch (deleteError) {
              console.log("로딩 메시지 삭제 실패:", deleteError);
            }
          }

          const roomSelectionBlocks = [
            {
              type: "section",
              text: {
                type: "mrkdwn",
                text: "🤔 *포커스룸이 2개가 있습니다. 어느 쪽을 원하시나요?*",
              },
            },
            {
              type: "actions",
              elements: parsedData.possibleRooms.map((roomName) => ({
                type: "button",
                text: {
                  type: "plain_text",
                  text: roomName, // Focus Room (East) 또는 Focus Room (West)
                  emoji: true,
                },
                style: roomName === "Focus Room (East)" ? "primary" : "danger",
                value: JSON.stringify({
                  selectedRoom: roomName,
                  originalRequest: {
                    date: parsedData.date,
                    time: parsedData.time,
                    attendeeCount: parsedData.attendeeCount,
                    duration: parsedData.duration,
                    isAvailabilityCheck: parsedData.isAvailabilityCheck,
                    isReservationRequest: parsedData.isReservationRequest,
                  },
                }),
                action_id: `focus_room_selection_${roomName.replace(/[^a-zA-Z0-9]/g, "_")}`,
              })),
            },
          ];

          await say({
            text: "포커스룸 선택이 필요합니다.",
            blocks: roomSelectionBlocks,
          });
          return;
        }

        // 날짜 설정
        const dateStr =
          parsedData.date || new Date().toISOString().split("T")[0];

        // 상태 확인 요청인 경우
        const roomData: {
          [roomName: string]: {
            events: {
              start: string;
              end: string;
              summary: string;
              attendees: { email: string; displayName?: string }[];
            }[];
          };
        } = {};

        // 특정 회의실이 지정된 경우 해당 회의실만 조회, 아니면 전체 조회
        const roomsToFetch = parsedData.room
          ? [parsedData.room]
          : Object.keys(ROOM_CALENDARS);

        console.log(`📋 조회할 회의실: ${roomsToFetch.join(", ")}`);

        for (const room of roomsToFetch) {
          try {
            const calendarId = ROOM_CALENDARS[room];
            if (calendarId) {
              const events = await getRoomEvents(calendarId, dateStr);
              roomData[room] = { events };
            } else {
              console.error(`❌ ${room}의 캘린더 ID를 찾을 수 없습니다.`);
              roomData[room] = { events: [] };
            }
          } catch (error) {
            console.error(`Error fetching events for ${room}:`, error);
            roomData[room] = { events: [] };
          }
        }

        console.log("상태 확인 요청 처리 중...");

        // 🆕 [MAIN] 로딩 메시지 삭제
        if (loadingMsg?.ts) {
          try {
            console.log("🔄 [MAIN] 로딩 메시지 삭제 시도:", loadingMsg.ts);
            await client.chat.delete({
              channel: loadingMsg.channel!,
              ts: loadingMsg.ts,
            });
            console.log("✅ [MAIN] 로딩 메시지 삭제 성공:", loadingMsg.ts);
          } catch (deleteError) {
            console.log("❌ [MAIN] 로딩 메시지 삭제 실패:", deleteError);
          }
        }

        // 🆕 [FOCUS] 로딩 메시지 삭제
        if (loadingMessage?.ts) {
          try {
            console.log("🔄 [FOCUS] 로딩 메시지 삭제 시도:", loadingMessage.ts);
            await client.chat.delete({
              channel: loadingMessage.channel!,
              ts: loadingMessage.ts,
            });
            console.log("✅ [FOCUS] 로딩 메시지 삭제 성공:", loadingMessage.ts);
          } catch (deleteError) {
            console.log("❌ [FOCUS] 로딩 메시지 삭제 실패:", deleteError);
          }
        }

        // 중복 메시지 전송 방지를 위해 주석 처리
        // try {
        //   const blocks = await getRoomReservationBlockKit(dateStr, roomData);
        //   await say({ blocks });
        // } catch (error) {
        //   console.error("Error building reservation blocks:", error);
        //   await say("예약 현황을 조회하는 중 오류가 발생했습니다.");
        // }

        // 예약 현황 조회인지 추천 요청인지 판단
        const isStatusCheck =
          parsedData.isAvailabilityCheck &&
          !parsedData.time &&
          !parsedData.attendeeCount &&
          !message.text.includes("추천") && // 🆕 추천 키워드가 있으면 현황 조회가 아님
          (message.text.includes("현황") ||
            message.text.includes("상황") ||
            message.text.includes("상태") ||
            message.text.includes("조회") ||
            (message.text.includes("예약") &&
              !message.text.includes("가능") &&
              !parsedData.time &&
              !parsedData.attendeeCount));

        console.log("🔍 분기 판단:", {
          isStatusCheck,
          isAvailabilityCheck: parsedData.isAvailabilityCheck,
          time: parsedData.time,
          attendeeCount: parsedData.attendeeCount,
          messageText: message.text,
        });

        if (isStatusCheck) {
          // 예약 현황 조회
          console.log("📊 예약 현황 조회 처리");

          // 🆕 로딩 메시지 삭제
          await safeDeleteLoadingMessage(loadingMsg, client);

          try {
            const blocks = await getRoomReservationBlockKit(dateStr, roomData);

            if (!blocks || blocks.length <= 2) {
              // 🆕 에러 시에도 로딩 메시지 삭제 (이미 위에서 삭제했지만 안전장치)
              await safeDeleteLoadingMessage(loadingMsg, client);

              await say({
                blocks: [
                  {
                    type: "section",
                    text: {
                      type: "mrkdwn",
                      text: ":warning: 죄송합니다. 예약 현황 조회에 실패하였습니다. 다시 한번 메세지 입력 부탁드립니다.",
                    },
                  },
                ],
                text: "죄송합니다. 예약 현황 조회에 실패하였습니다. 다시 한번 메세지 입력 부탁드립니다.",
              });
              return;
            }

            const dateObj = new Date(dateStr);
            const dateFormatted = dateObj.toLocaleDateString("ko-KR", {
              year: "numeric",
              month: "long",
              day: "numeric",
            });

            // 특정 회의실이 지정된 경우와 전체 회의실 조회 경우에 따라 다른 메시지 표시
            const responseText = parsedData.room
              ? `${dateFormatted} ${parsedData.room} 예약 현황입니다.`
              : `${dateFormatted} 회의실 예약 현황입니다.`;

            await say({
              text: responseText,
              blocks: blocks.slice(0, 50),
            });
          } catch (error) {
            console.error("Error getting room reservation blocks:", error);
            await say(
              ":warning: 죄송합니다. 예약 현황 조회에 실패하였습니다. 다시 한번 메세지 입력 부탁드립니다."
            );
          }
        } else {
          // 추천 시스템 실행
          console.log("🎯 추천 시스템 처리");

          try {
            let timeValue: string | undefined;
            if (parsedData.time) {
              // "14:00" 형식에서 시간만 추출
              const hourMatch = parsedData.time.match(/^(\d{1,2})/);
              timeValue = hourMatch ? hourMatch[1] : undefined;
            }

            const recommendation = await recommendAvailableRooms(
              roomData,
              dateStr,
              timeValue,
              parsedData.attendeeCount
            );

            // 🆕 로딩 메시지 삭제
            await safeDeleteLoadingMessage(loadingMsg, client);

            if (recommendation) {
              const blocks = buildRecommendationBlocks(
                recommendation,
                message.user,
                message.channel,
                dateStr,
                userName,
                parsedData.time || undefined, // targetTime 매개변수 추가 (null을 undefined로 변환)
                parsedData.room || undefined, // specificRoom 매개변수 추가 (null을 undefined로 변환)
                parsedData.duration || undefined // duration 매개변수 추가
              );

              await say({
                text: recommendation.message,
                blocks: blocks,
              });
            } else {
              await say("죄송합니다. 추천 시스템에 문제가 발생했습니다.");
            }
          } catch (error) {
            console.error("Error in recommendation system:", error);

            // 🆕 로딩 메시지 삭제
            await safeDeleteLoadingMessage(loadingMsg, client);

            await say("죄송합니다. 추천 시스템에 문제가 발생했습니다.");
          }
        }
        return; // 조회/추천 시스템 실행 후 종료
      }

      // 기존 예약 상태가 있고 새로운 요청이 아닌 경우에만 예약 프로세스 계속
      if (existingStateResult) {
        const [stateId, state] = existingStateResult;

        // 입력 상태가 NONE이면 추가 처리하지 않음
        if (state.inputState === "NONE") {
          console.log("입력 상태가 NONE이므로 메시지 무시");
          return;
        }

        if (state.inputState === "TIME" && !state.selectedStartTime) {
          // 시작 시간 입력 대기 중
          const timeInput = parseTimeInput(message.text);
          if (timeInput) {
            if (
              isTimeInRange(timeInput, {
                start: state.availableSlot.start,
                end: state.availableSlot.end,
              })
            ) {
              reservationStateManager.updateState(stateId, {
                selectedStartTime: timeInput,
                inputState: "DURATION", // 다음 단계로 이동
              });

              const durationBlocks = createDurationSelectionBlocks(
                stateId,
                timeInput
              );

              await say({
                text: `시작 시간 ${timeInput}가 선택되었습니다.`,
                blocks: durationBlocks,
              });
              return;
            } else {
              await say(
                `❌ 입력하신 시간(${timeInput})은 예약 가능 시간(${state.availableSlot.start}~${state.availableSlot.end}) 범위를 벗어납니다. 다시 입력해주세요.`
              );
              return;
            }
          } else {
            await say(
              "❌ 올바른 시간 형식으로 입력해주세요. (예: 13시, 13시 30분, 13:00, 13:30)"
            );
            return;
          }
        } else if (state.inputState === "DURATION" && !state.selectedDuration) {
          // 예약 시간 입력 대기 중
          const durationInput = parseDuration(message.text);
          if (durationInput && durationInput > 0) {
            reservationStateManager.updateState(stateId, {
              selectedDuration: durationInput,
              inputState: "NONE", // 입력 완료
            });

            const confirmationBlocks = createConfirmationBlocks(
              stateId,
              reservationStateManager.getState(stateId)!
            );

            await say({
              text: `예약 시간 ${durationInput}분이 선택되었습니다.`,
              blocks: confirmationBlocks,
            });
            return;
          } else {
            await say(
              "❌ 올바른 시간 형식으로 입력해주세요. (예: 30분, 1시간, 1시간 30분)"
            );
            return;
          }
        }
      }

      // 기본 응답 처리 (추천 시스템을 실행하지 않은 경우)
      if (
        !parsedData.isAvailabilityCheck &&
        !parsedData.isReservationRequest &&
        !parsedData.isRoomInfoRequest
      ) {
        // 알지 못하는 메시지에 대한 기본 응답
        await say(
          "저는 pxd 회의실 예약을 돕는 슬렉 봇입니다. 회의실 예약과 관련 없는 질문에는 대답할 수 없어요 :smiling_face_with_tear:"
        );
        return;
      }

      // 헬프/가이드 키워드가 포함된 경우 roomInfo 대신 chitchat으로 강제 분기
      const helpKeywords = [
        "이용 방법",
        "예약 방법",
        "어떻게 써",
        "사용법",
        "help",
        "도움말",
        "예약하는 법",
        "예약 절차",
        "예약 프로세스",
      ];
      if (
        parsedData.isRoomInfoRequest &&
        helpKeywords.some((kw) => message.text && message.text.includes(kw))
      ) {
        parsedData.isGreetingOrChitchat = true;
        parsedData.isRoomInfoRequest = false;
      }
    }
  } catch (error) {
    console.error("Error in message handler:", error);
    await say("죄송합니다. 메시지 처리 중 오류가 발생했습니다.");
  }
});

// 다른 시간 입력하기 버튼 핸들러
app.action("custom_time", async ({ body, ack, respond }) => {
  await ack();

  try {
    const actionBody = body as { actions: Array<{ value: string }> };
    const payload = JSON.parse(actionBody.actions[0].value);
    const { stateId } = payload;

    const state = reservationStateManager.getState(stateId);
    if (!state) {
      throw new Error("예약 상태를 찾을 수 없습니다.");
    }

    await respond({
      blocks: createTimeInputPromptBlocks(stateId, state.availableSlot),
      replace_original: true,
    });
  } catch (error) {
    console.error("Error handling custom time selection:", error);
    await respond({
      text: "시간 선택 처리 중 오류가 발생했습니다.",
      replace_original: false,
    });
  }
});

// 메시지 입력 처리 (시간 및 회의 시간)
app.message(async ({ message, say }) => {
  if (!("text" in message) || typeof message.text !== "string") {
    console.log("Message skipped: not a text message");
    return;
  }

  const msg = message as any;

  try {
    console.log("Processing message:", {
      text: message.text,
      channel: msg.channel,
    });

    // 새로운 회의실 요청인지 확인
    const isNewRoomRequest =
      message.text.includes("회의실") ||
      message.text.includes("예약") ||
      message.text.includes("추천") ||
      message.text.includes("현황") ||
      message.text.includes("상황") ||
      message.text.includes("상태") ||
      message.text.includes("조회") ||
      message.text.includes("내일") ||
      message.text.includes("오늘") ||
      message.text.includes("금요일") ||
      message.text.includes("월요일") ||
      message.text.includes("화요일") ||
      message.text.includes("수요일") ||
      message.text.includes("목요일") ||
      message.text.includes("토요일") ||
      message.text.includes("일요일");

    // 예약 상태 확인 - 채널 기반으로 변경
    const stateInfo = reservationStateManager.getStateByChannel(msg.channel);

    console.log("Retrieved state info:", stateInfo);

    // 새로운 회의실 요청이면 기존 상태 삭제
    if (isNewRoomRequest && stateInfo) {
      const [stateId] = stateInfo;
      console.log("기존 예약 상태 삭제 후 새로운 요청 처리");
      reservationStateManager.deleteState(stateId);
      return; // 새로운 요청은 메인 핸들러에서 처리되도록 함
    }

    if (!stateInfo) {
      console.log("No reservation state found for channel:", msg.channel);
      return;
    }

    const [stateId, state] = stateInfo;
    console.log("Current reservation state:", {
      stateId,
      state,
    });

    // 입력 상태에 따라 다르게 처리
    if (state.inputState === "TIME") {
      // 시작 시간 입력 처리
      const parsedTime = parseTimeInput(message.text);
      console.log("Parsed time:", parsedTime);

      if (!parsedTime) {
        await say({
          text: "올바른 시간 형식이 아닙니다. 다시 입력해주세요.\n(예: 13시, 13시 30분, 13:00, 13:30)",
        });
        return;
      }

      if (!isTimeInRange(parsedTime, state.availableSlot)) {
        await say({
          text: `${state.availableSlot.start}~${state.availableSlot.end} 사이의 시간을 입력해주세요.`,
        });
        return;
      }

      // 상태 업데이트
      reservationStateManager.updateState(stateId, {
        selectedStartTime: parsedTime,
        inputState: "DURATION", // 다음은 회의 시간 입력
      });

      // 회의 시간 선택 프롬프트 표시
      await say({
        blocks: createDurationSelectionBlocks(stateId, parsedTime),
      });
    } else if (state.inputState === "DURATION") {
      // 회의 시간 입력 처리
      if (!state.selectedStartTime) {
        console.log("No start time selected");
        return;
      }

      const duration = parseDuration(message.text);
      if (!duration) {
        await say({
          text: "올바른 시간 형식이 아닙니다. 다시 입력해주세요.\n(예: 1시간 30분, 90분)",
        });
        return;
      }

      // 선택한 시간이 가능한 범위 내인지 확인
      const endTime = addMinutesToTime(state.selectedStartTime, duration);
      if (endTime > state.availableSlot.end) {
        await say({
          text: `선택하신 시간(${duration}분)은 예약 가능 범위를 초과합니다. 다른 시간을 선택해주세요.`,
        });
        return;
      }

      // 상태 업데이트
      reservationStateManager.updateState(stateId, {
        selectedDuration: duration,
        inputState: "NONE", // 입력 완료
      });

      // 최종 확인 메시지 표시
      await say({
        blocks: createConfirmationBlocks(stateId, {
          ...state,
          selectedDuration: duration,
        }),
      });
    }
  } catch (error) {
    console.error("Error handling message:", error);
  }
});

// 직접 입력 처리
app.action("custom_duration", async ({ body, ack, respond }) => {
  await ack();

  try {
    const actionBody = body as { actions: Array<{ value: string }> };
    const payload = JSON.parse(actionBody.actions[0].value);
    const { stateId } = payload;

    const state = reservationStateManager.getState(stateId);
    if (!state) {
      throw new Error("예약 상태를 찾을 수 없습니다.");
    }

    // 상태 업데이트 - 회의 시간 입력 대기
    reservationStateManager.updateState(stateId, {
      inputState: "DURATION", // 회의 지속 시간 입력 대기
    });

    await respond({
      text: "원하시는 회의 시간을 입력해주세요.\n(예: 1시간 30분, 90분)",
      replace_original: false,
    });
  } catch (error) {
    console.error("Error handling custom duration input:", error);
    await respond({
      text: "시간 입력 처리 중 오류가 발생했습니다.",
      replace_original: false,
    });
  }
});

// 예약 확인 핸들러
app.action<BlockButtonAction>(
  "confirm_reservation",
  async ({ body, ack, respond, client }) => {
    await ack();

    try {
      if (!body.actions?.[0]?.value) {
        throw new Error("Invalid button payload");
      }

      const payload = JSON.parse(body.actions[0].value);
      const { stateId } = payload;

      const state = reservationStateManager.getState(stateId);
      if (!state || !state.selectedStartTime || !state.selectedDuration) {
        throw new Error("예약 상태가 유효하지 않습니다.");
      }

      // Google Calendar 링크로 대체
      const userName = "사용자";
      const calendarId = ROOM_CALENDARS[state.roomName];
      const startDateObj = new Date(`${state.date}T${state.selectedStartTime}`);
      const endDateObj = new Date(
        startDateObj.getTime() + state.selectedDuration * 60000
      );
      const calendarUrl = makeCalendarUrl({
        roomName: state.roomName,
        userName,
        startDate: startDateObj,
        endDate: endDateObj,
        calendarId,
      });

      // 상태를 즉시 NONE으로 변경하여 추가 입력 처리 방지
      reservationStateManager.updateState(stateId, {
        inputState: "NONE",
      });

      await respond({
        blocks: [
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: `📅 *${state.roomName}* 회의실 예약 정보\n\n시간: ${state.selectedStartTime}~${addMinutesToTime(state.selectedStartTime, state.selectedDuration)}\n\n아래 버튼을 클릭하여 Google Calendar에서 예약을 완료해주세요.`,
            },
          },
          {
            type: "actions",
            elements: [
              {
                type: "button",
                text: {
                  type: "plain_text",
                  text: "Google Calendar에서 예약하기",
                },
                style: "primary",
                url: calendarUrl,
              },
            ],
          },
        ],
        replace_original: true,
      });

      // 일정 시간 후 상태 정리 (사용자가 구글 캘린더에서 예약을 완료할 시간을 줌)
      setTimeout(() => {
        reservationStateManager.deleteState(stateId);
      }, 60000); // 1분 후 상태 삭제
    } catch (error) {
      console.error("Error handling reservation confirmation:", error);
      await respond({
        text: "회의실 예약 처리 중 오류가 발생했습니다.",
        replace_original: false,
      });
    }
  }
);

// 예약 취소 핸들러
app.action("cancel_reservation", async ({ body, ack, respond }) => {
  await ack();

  try {
    const actionBody = body as { actions: Array<{ value: string }> };
    const payload = JSON.parse(actionBody.actions[0].value);
    const { stateId } = payload;

    // 상태 정리
    reservationStateManager.deleteState(stateId);

    await respond({
      text: "회의실 예약이 취소되었습니다.",
      replace_original: true,
    });
  } catch (error) {
    console.error("Error handling reservation cancellation:", error);
    await respond({
      text: "예약 취소 처리 중 오류가 발생했습니다.",
      replace_original: false,
    });
  }
});

// 예약 시작 버튼 클릭 핸들러
app.action<BlockButtonAction>(
  "start_reservation",
  async ({ ack, body, client }) => {
    await ack();

    try {
      if (!body.actions?.[0]?.value) {
        throw new Error("Invalid button payload");
      }

      const payload = JSON.parse(body.actions[0].value);
      const { roomName, startTime, endTime, stateId } = payload;

      console.log("Starting reservation process:", {
        stateId,
        roomName,
        messageTs: body.message?.ts,
        threadTs: body.message?.thread_ts,
      });

      // 예약 프로세스 시작
      const blocks: any[] = [
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text: `:pushpin: *${roomName}* 회의실 예약\n\n원하시는 시작 시간을 입력해주세요. \`(예: 14:30, 오후 3시)\`\n또는 아래 버튼에서 선택하세요:`,
          },
        },
      ];

      // 스레드에 응답
      const response = await client.chat.postMessage({
        channel: body.channel?.id || "",
        thread_ts: body.message?.thread_ts || body.message?.ts,
        blocks: blocks,
      });

      // 예약 상태 저장
      reservationStateManager.updateState(stateId, {
        messageTs: response.ts,
        threadTs: body.message?.thread_ts || body.message?.ts,
        inputState: "TIME",
        roomName: roomName, // 선택된 회의실 이름 저장
        availableSlot: {
          start: startTime,
          end: endTime,
        },
      });
    } catch (error) {
      console.error("Error starting reservation:", error);
    }
  }
);

// 회의 시간 선택 핸들러
app.action(/^select_duration_\d+$/, async ({ body, ack, respond }) => {
  await ack();

  try {
    const actionBody = body as { actions: Array<{ value: string }> };
    const payload = JSON.parse(actionBody.actions[0].value);
    const { stateId, duration } = payload;

    const state = reservationStateManager.getState(stateId);
    if (!state || !state.selectedStartTime) {
      throw new Error("예약 상태가 유효하지 않습니다.");
    }

    // 선택한 시간이 가능한 범위 내인지 확인
    const endTime = addMinutesToTime(state.selectedStartTime, duration);
    if (endTime > state.availableSlot.end) {
      await respond({
        text: `선택하신 시간(${duration}분)은 예약 가능 범위를 초과합니다. 다른 시간을 선택해주세요.`,
        replace_original: false,
      });
      return;
    }

    // 상태 업데이트
    reservationStateManager.updateState(stateId, {
      selectedDuration: duration,
      inputState: "NONE", // 입력 완료
    });

    // 최종 확인 메시지 표시
    await respond({
      blocks: createConfirmationBlocks(stateId, {
        ...state,
        selectedDuration: duration,
      }),
      replace_original: true,
    });
  } catch (error) {
    console.error("Error handling duration selection:", error);
    await respond({
      text: "회의 시간 선택 처리 중 오류가 발생했습니다.",
      replace_original: false,
    });
  }
});

// 회의실 선택 후 지속 시간 선택 핸들러
app.action(
  /^select_room_for_duration_/,
  async ({ body, ack, respond, client }) => {
    await ack();

    try {
      const actionBody = body as {
        actions: Array<{ value: string }>;
        message?: { ts: string };
        channel?: { id: string };
        user?: { id: string };
      };

      if (!actionBody.actions?.[0]?.value) {
        throw new Error("Invalid button payload");
      }

      const payload = JSON.parse(actionBody.actions[0].value);
      const { room, date, startTime, maxEndHour } = payload;

      // 사용자 정보 가져오기
      const userId = actionBody.user?.id;
      let userName = "사용자";

      if (userId) {
        try {
          const userInfo = await client.users.info({ user: userId });
          userName =
            userInfo.user?.real_name || userInfo.user?.name || "사용자";
        } catch (error) {
          console.error("Error fetching user info:", error);
        }
      }

      // 최대 예약 가능한 시간(분 단위) 계산
      const maxAvailableMinutes = (maxEndHour - startTime) * 60;

      // 디폴트 1시간(60분) 또는 최대 예약 가능한 시간 중 작은 값 선택
      const defaultDuration = Math.min(60, maxAvailableMinutes);

      // 종료 시간 계산
      const endHour = startTime + Math.floor(defaultDuration / 60);
      const endMinute = defaultDuration % 60;
      const endTimeStr = `${endHour.toString().padStart(2, "0")}:${endMinute.toString().padStart(2, "0")}`;

      // 날짜와 시간을 올바르게 설정
      const startDate = new Date(date + "T00:00:00");
      startDate.setHours(startTime, 0, 0, 0);
      const endDate = new Date(startDate.getTime() + defaultDuration * 60000);

      // Google Calendar URL 생성 (회의실 리소스 포함)
      const calendarId = ROOM_CALENDARS[room];
      const calendarUrl = makeCalendarUrl({
        roomName: room,
        userName,
        startDate,
        endDate,
        calendarId,
      });

      // 지속 시간 표시를 위한 텍스트 생성
      const durationText = makeDurationText(defaultDuration);

      // 일반 채널에 메시지 전송
      await client.chat.postMessage({
        channel: actionBody.channel?.id || "",
        blocks: [
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: makeReservationMessage({
                roomName: room,
                userName,
                date,
                startTime: `${startTime}:00`,
                endTime: endTimeStr,
                durationMinutes: defaultDuration,
              }),
              // text: `*${room}* 회의실 예약을 진행합니다.\n\n👤 주최자: ${userName}\n\n📅 ${date}\n\n⏰ ${startTime}:00 ~ ${endTimeStr} (${durationText})`,
            },
          },
          {
            type: "actions",
            elements: [
              {
                type: "button",
                text: {
                  type: "plain_text",
                  text: "Google Calendar에서 예약하기",
                },
                style: "primary",
                url: calendarUrl,
              },
              {
                type: "button",
                text: {
                  type: "plain_text",
                  text: "🕐 종료 시간 변경",
                },
                value: JSON.stringify({
                  room: room,
                  date: date,
                  startTime: startTime,
                  maxEndHour: maxEndHour,
                  userName: userName,
                  action: "modify_duration",
                }),
                action_id: "modify_meeting_duration_advanced",
              },
            ],
          },
        ],
        text: "회의실 예약",
      });
    } catch (error) {
      console.error("Error handling room selection for duration:", error);
      await respond({
        text: "회의실 선택 처리 중 오류가 발생했습니다.",
        replace_original: false,
      });
    }
  }
);

// 지속 시간 선택 후 예약 진행 핸들러
app.action(
  /^proceed_with_selected_duration_/,
  async ({ body, ack, respond, client }) => {
    await ack();

    try {
      const actionBody = body as any;

      // 사용자 정보 가져오기
      const userId = actionBody.user?.id;
      let userName = "사용자";

      if (userId) {
        try {
          const userInfo = await client.users.info({ user: userId });
          userName =
            userInfo.user?.real_name || userInfo.user?.name || "사용자";
        } catch (error) {
          console.error("Error fetching user info:", error);
        }
      }

      // 버튼의 value에서 정보 가져오기
      const payload = JSON.parse(actionBody.actions[0].value);
      const { room, date, startTime, duration } = payload;

      // 종료 시간 계산
      const endHour = startTime + Math.floor(duration / 60);
      const endMinute = duration % 60;
      const endTimeStr = `${endHour.toString().padStart(2, "0")}:${endMinute.toString().padStart(2, "0")}`;

      // 날짜와 시간을 올바르게 설정
      const startDate = new Date(date + "T00:00:00");
      startDate.setHours(startTime, 0, 0, 0);
      const endDate = new Date(startDate.getTime() + duration * 60000);

      // Google Calendar URL 생성 (회의실 리소스 포함)
      const calendarId = ROOM_CALENDARS[room];
      const calendarUrl = makeCalendarUrl({
        roomName: room,
        userName,
        startDate,
        endDate,
        calendarId,
      });

      await client.chat.postMessage({
        channel: actionBody.channel?.id || "",
        blocks: [
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: makeReservationMessage({
                roomName: room,
                userName,
                date,
                startTime: `${startTime}:00`,
                endTime: endTimeStr,
                durationMinutes: duration,
              }),
              // text: `*${room}* 회의실 예약을 진행합니다.\n\n👤 주최자: ${userName}\n\n📅 ${date}\n\n⏰ ${startTime}:00 ~ ${endTimeStr} (${duration}분)`,
            },
          },
          {
            type: "actions",
            elements: [
              {
                type: "button",
                text: {
                  type: "plain_text",
                  text: "Google Calendar에서 예약하기",
                },
                style: "primary",
                url: calendarUrl,
              },
              {
                type: "button",
                text: {
                  type: "plain_text",
                  text: "🕐 종료 시간 변경",
                },
                value: JSON.stringify({
                  room: room,
                  date: date,
                  selectedTime: startTime,
                  userName: userName,
                  action: "modify_duration",
                }),
                action_id: "modify_meeting_duration",
              },
            ],
          },
        ],
        text: "회의실 예약",
      });
    } catch (error) {
      console.error("Error handling duration confirmation:", error);
      await client.chat.postMessage({
        channel: (body as any).channel?.id || "",
        text: "예약 처리 중 오류가 발생했습니다.",
      });
    }
  }
);

// 회의실 추천에서 지속 시간 확인 후 예약 진행 핸들러
app.action("custom_time_input", async ({ body, ack, respond, client }) => {
  await ack();

  try {
    const actionBody = body as {
      actions: Array<{ value: string }>;
      channel?: { id: string };
    };

    const payload = JSON.parse(actionBody.actions[0].value);
    const { roomName, availableSlots, date, stateId } = payload;

    // 예약 상태 업데이트
    reservationStateManager.updateState(stateId, {
      inputState: "TIME",
    });

    // 예약 가능한 시간대들을 30분 단위로 시간 버튼 생성 (제한 없이)
    const timeButtons = [];

    if (availableSlots && Array.isArray(availableSlots)) {
      for (const slot of availableSlots) {
        const slotStart = new Date(`${date}T${slot.start}:00`);
        const slotEnd = new Date(`${date}T${slot.end}:00`);
        let current = new Date(slotStart);

        while (current < slotEnd) {
          // 최소 예약 단위(30분) 이상 가능한 경우만 버튼 생성
          const next = new Date(current.getTime() + 30 * 60000);
          if (next <= slotEnd) {
            const timeStr = `${String(current.getHours()).padStart(2, "0")}:${String(current.getMinutes()).padStart(2, "0")}`;
            timeButtons.push({
              type: "button",
              text: {
                type: "plain_text",
                text: timeStr,
              },
              value: JSON.stringify({
                room: roomName,
                date: date,
                time: timeStr,
              }),
              action_id: `select_time_${timeStr.replace(":", "")}`,
            });
          }
          // 30분 추가
          current.setMinutes(current.getMinutes() + 30);
        }
      }
    }

    const blocks: any[] = [
      {
        type: "section",
        text: {
          type: "mrkdwn",
          text: `${makeRoomIcon(roomName)} *${roomName}* 회의실 예약\n\n원하시는 시작 시간을 입력해주세요. \`(예: 14:30, 오후 3시)\`\n또는 아래 버튼에서 선택하세요:`,
        },
      },
    ];

    // 시간 버튼들을 5개씩 나누어서 추가 (제한 없이 모든 버튼)
    if (timeButtons.length > 0) {
      for (let i = 0; i < timeButtons.length; i += 5) {
        const buttonGroup = timeButtons.slice(i, i + 5);
        blocks.push({
          type: "actions",
          elements: buttonGroup,
        });
      }
    }

    await client.chat.postMessage({
      channel: actionBody.channel?.id || "",
      blocks: blocks,
      text: `${roomName} 회의실 시간 선택`,
    });
  } catch (error) {
    console.error("Error handling custom time input:", error);
    await client.chat.postMessage({
      channel: (body as any).channel?.id || "",
      text: "시간 입력 처리 중 오류가 발생했습니다.",
    });
  }
});

// 회의실 추천에서 지속 시간 확인 후 예약 진행 핸들러
app.action(
  "proceed_with_duration_from_recommendation",
  async ({ body, ack, respond, client }) => {
    await ack();

    try {
      const actionBody = body as any;

      // 원본 메시지의 ts를 thread_ts로 사용
      const threadTs = actionBody.message?.ts;

      // 버튼의 value에서 기본 정보 가져오기
      const buttonPayload = JSON.parse(actionBody.actions[0].value);
      const { roomName, date, selectedTime, userName, stateId } = buttonPayload;

      // 라디오 버튼에서 선택된 지속 시간 찾기
      const selectedDuration = actionBody.state?.values
        ? Object.values(actionBody.state.values).find(
            (section: any) =>
              section.duration_selection_from_recommendation?.selected_option
          )
        : null;

      if (
        !selectedDuration ||
        !(selectedDuration as any).duration_selection_from_recommendation
          ?.selected_option
      ) {
        await client.chat.postMessage({
          channel: actionBody.channel?.id || "",
          thread_ts: threadTs,
          text: "지속 시간을 선택해주세요.",
        });
        return;
      }

      const durationMinutes = parseInt(
        (selectedDuration as any).duration_selection_from_recommendation
          .selected_option.value
      );

      // 종료 시간 계산
      const [startHour, startMinute] = selectedTime.split(":").map(Number);
      const endHour = Math.floor(
        (startHour * 60 + startMinute + durationMinutes) / 60
      );
      const endMinute = (startHour * 60 + startMinute + durationMinutes) % 60;
      const endTime = `${endHour.toString().padStart(2, "0")}:${endMinute.toString().padStart(2, "0")}`;

      const startDateTime = new Date(`${date}T${selectedTime}:00`);
      const endDateTime = new Date(`${date}T${endTime}:00`);

      // userName은 버튼 payload에서 받은 값 사용
      const calendarId2 = ROOM_CALENDARS[roomName];
      const googleCalendarUrl = makeCalendarUrl({
        roomName,
        userName,
        startDate: startDateTime,
        endDate: endDateTime,
        calendarId: calendarId2,
      });

      // 최종 예약 링크 메시지
      await client.chat.postMessage({
        channel: actionBody.channel?.id || "",
        thread_ts: threadTs,
        blocks: [
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: `✅ *${roomName}* 회의실 예약이 준비되었습니다!\n\n📅 날짜: ${date}\n\n⏰ 시간: ${selectedTime} - ${endTime}\n\n👤 예약자: ${userName}`,
            },
          },
          {
            type: "actions",
            elements: [
              {
                type: "button",
                text: {
                  type: "plain_text",
                  text: "Google Calendar에 추가",
                },
                style: "primary",
                url: googleCalendarUrl,
              },
            ],
          },
        ],
        text: "회의실 예약",
      });

      // 예약 상태 정리
      reservationStateManager.deleteState(stateId);
    } catch (error) {
      console.error(
        "Error proceeding with duration from recommendation:",
        error
      );
      await client.chat.postMessage({
        channel: (body as any).channel?.id || "",
        thread_ts: (body as any).message?.ts,
        text: "예약 진행 중 오류가 발생했습니다.",
      });
    }
  }
);

// 시간 선택 핸들러 (버튼 클릭)
app.action(/^select_time_/, async ({ body, ack, respond, client }) => {
  await ack();

  try {
    const actionBody = body as {
      actions: Array<{ value: string }>;
      channel?: { id: string };
      message?: { ts: string };
    };

    const payload = JSON.parse(actionBody.actions[0].value);
    const { room, date, time } = payload;

    // 사용자 정보 가져오기
    const userId = (body as any).user?.id;
    let userName = "사용자";

    if (userId) {
      try {
        const userInfo = await client.users.info({ user: userId });
        userName = userInfo.user?.real_name || userInfo.user?.name || "사용자";
      } catch (error) {
        console.error("Error fetching user info:", error);
      }
    }

    // 시작 시간 파싱
    const startHour = parseInt(time.split(":")[0]);
    const startMinute = parseInt(time.split(":")[1]);

    // 구글 캘린더에서 해당 날짜의 이벤트들을 가져오기
    const calendarId = ROOM_CALENDARS[room];
    const events = await getRoomEvents(calendarId, date);

    // 선택한 시작 시간부터 실제 예약 가능한 최대 시간 계산
    const maxEndTime = getMaxAvailableTime(events, startHour, startMinute);
    const totalAvailableMinutes =
      maxEndTime.hour * 60 + maxEndTime.minute - (startHour * 60 + startMinute);

    // 30분 단위로 예약 가능한 시간 옵션들 생성
    const durationOptions = createDurationOptions(
      startHour,
      startMinute,
      maxEndTime
    );

    // 30분 미만 예약 불가 처리
    if (totalAvailableMinutes < 30 || durationOptions.length === 0) {
      await client.chat.postMessage({
        channel: actionBody.channel?.id || "",
        text: "선택하신 시간에는 예약 가능한 시간이 없습니다. 다른 시간을 선택해주세요.",
      });
      return;
    }

    // 기본 예약 시간(최대 1시간, 30분만 가능하면 30분)
    const defaultDuration =
      durationOptions.find((opt) => opt.duration === 60)?.duration ||
      durationOptions[0]?.duration ||
      30;
    const endDate = new Date(date + "T" + time);
    endDate.setMinutes(endDate.getMinutes() + defaultDuration);
    const endTime = `${endDate.getHours().toString().padStart(2, "0")}:${endDate.getMinutes().toString().padStart(2, "0")}`;

    // 예약 메시지 생성
    await client.chat.postMessage({
      channel: actionBody.channel?.id || "",
      blocks: [
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text: makeReservationMessage({
              roomName: room,
              userName,
              date,
              startTime: time,
              endTime,
              durationMinutes: defaultDuration,
            }),
          },
        },
        {
          type: "actions",
          elements: [
            {
              type: "button",
              text: {
                type: "plain_text",
                text: "Google Calendar에서 예약하기",
              },
              style: "primary",
              url: makeCalendarUrl({
                roomName: room,
                userName,
                startDate: new Date(date + "T" + time),
                endDate,
                calendarId,
              }),
            },
            {
              type: "button",
              text: {
                type: "plain_text",
                text: "🕐 종료 시간 변경",
              },
              value: JSON.stringify({
                room: room,
                date: date,
                selectedTime: time,
                userName: userName,
                action: "modify_duration",
              }),
              action_id: "modify_meeting_duration",
            },
          ],
        },
      ],
      text: "회의실 예약",
    });
  } catch (error) {
    console.error("Error handling time selection:", error);
    await client.chat.postMessage({
      channel: (body as any).channel?.id || "",
      text: "시간 선택 처리 중 오류가 발생했습니다.",
    });
  }
});

// 회의 시간 수정 버튼 핸들러 (고급)
app.action(
  "modify_meeting_duration",
  async ({ body, ack, respond, client }) => {
    await ack();

    try {
      const actionBody = body as {
        actions: Array<{ value: string }>;
        channel?: { id: string };
        message?: { ts: string };
        user?: { id: string };
      };

      const payload = JSON.parse(actionBody.actions[0].value);
      const { room, date, selectedTime, startTime, userName } = payload;
      // selectedTime 또는 startTime 중 하나 사용
      const timeStr = selectedTime || startTime;
      const [startHour, startMinute] = timeStr.split(":").map(Number);

      // 실제 예약 가능한 시간 계산
      let maxAvailableMinutes = (22 - startHour) * 60 - startMinute;
      let durationOptions: Array<{ duration: number; label: string }> = [];
      try {
        // 구글 캘린더에서 해당 날짜의 이벤트들을 가져오기
        const calendarId = ROOM_CALENDARS[room];
        const events = await getRoomEvents(calendarId, date);

        // 실제 예약 가능한 최대 시간 계산 (분 단위)
        const maxEndTime = getMaxAvailableTime(events, startHour, startMinute);
        maxAvailableMinutes = Math.min(
          maxAvailableMinutes,
          maxEndTime.hour * 60 +
            maxEndTime.minute -
            (startHour * 60 + startMinute)
        );
        // 30분 단위로 가능한 옵션들만 생성
        durationOptions = createDurationOptions(
          startHour,
          startMinute,
          maxEndTime
        );
      } catch (error) {
        console.error(
          "Error fetching calendar events for duration calculation:",
          error
        );
        // 오류 발생 시 기본값 사용
      }

      // 30분 미만 예약 불가 처리
      if (maxAvailableMinutes < 30 || durationOptions.length === 0) {
        await client.chat.postMessage({
          channel: actionBody.channel?.id || "",
          text: "선택하신 시간에는 예약 가능한 시간이 없습니다. 다른 시간을 선택해주세요.",
        });
        return;
      }

      const blocks = [
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text: `${makeRoomIcon(room)} *${room}* 회의실 시간 수정\n\n📅 날짜: ${date}\n⏰ 시작 시간: ${timeStr}\n\n회의 지속 시간을 선택해주세요:`,
          },
        },
        {
          type: "actions",
          elements: durationOptions
            .slice(0, 4)
            .map((option: { duration: number; label: string }) => ({
              type: "button",
              text: {
                type: "plain_text",
                text: option.label,
              },
              value: JSON.stringify({
                room: room,
                date: date,
                selectedTime: timeStr,
                userName: userName,
                duration: option.duration,
                originalMessageTs: actionBody.message?.ts,
              }),
              action_id: `confirm_duration_${option.duration}`,
            })),
        },
      ];
      if (durationOptions.length > 4) {
        blocks.push({
          type: "actions",
          elements: durationOptions
            .slice(4)
            .map((option: { duration: number; label: string }) => ({
              type: "button",
              text: {
                type: "plain_text",
                text: option.label,
              },
              value: JSON.stringify({
                room: room,
                date: date,
                selectedTime: timeStr,
                userName: userName,
                duration: option.duration,
                originalMessageTs: actionBody.message?.ts,
              }),
              action_id: `confirm_duration_${option.duration}`,
            })),
        });
      }

      await client.chat.postMessage({
        channel: actionBody.channel?.id || "",
        blocks: blocks,
        text: "회의 시간 수정",
      });
    } catch (error) {
      console.error("Error handling duration modification (통합):", error);
      await client.chat.postMessage({
        channel: (body as any).channel?.id || "",
        text: "시간 수정 처리 중 오류가 발생했습니다.",
      });
    }
  }
);

// 지속 시간 확정 핸들러 (고급)
app.action(
  /^confirm_duration_advanced_/,
  async ({ body, ack, respond, client }) => {
    await ack();

    try {
      const actionBody = body as {
        actions: Array<{ value: string }>;
        channel?: { id: string };
        message?: { ts: string };
      };

      const payload = JSON.parse(actionBody.actions[0].value);
      const { room, date, startTime, userName, duration, originalMessageTs } =
        payload;

      // 종료 시간 계산
      const endTotalMinutes = startTime * 60 + duration;
      const endHour = Math.floor(endTotalMinutes / 60);
      const endMinute = endTotalMinutes % 60;
      const endTime = `${endHour.toString().padStart(2, "0")}:${endMinute.toString().padStart(2, "0")}`;

      // 날짜와 시간을 올바르게 설정
      const startDate = new Date(date + "T00:00:00");
      startDate.setHours(startTime, 0, 0, 0);
      const endDate = new Date(startDate.getTime() + duration * 60 * 1000);

      // Google Calendar URL 생성 (회의실 리소스 포함)
      const calendarId = ROOM_CALENDARS[room];
      const calendarUrl = makeCalendarUrl({
        roomName: room,
        userName,
        startDate,
        endDate,
        calendarId,
      });

      // 지속 시간 표시를 위한 텍스트 생성
      const durationText = makeDurationText(duration);

      // 최종 예약 메시지 전송
      await client.chat.postMessage({
        channel: actionBody.channel?.id || "",
        blocks: [
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: makeReservationMessage({
                roomName: room,
                userName,
                date,
                startTime: `${startTime}:00`,
                endTime,
                durationMinutes: duration,
              }),
              // text: `*${room}* 회의실 예약을 진행합니다.\n\n👤 주최자: ${userName}\n\n📅 ${date}\n\n⏰ ${startTime}:00 ~ ${endTime} (${durationText})`,
            },
          },
          {
            type: "actions",
            elements: [
              {
                type: "button",
                text: {
                  type: "plain_text",
                  text: "Google Calendar에서 예약하기",
                },
                style: "primary",
                url: calendarUrl,
              },
              {
                type: "button",
                text: {
                  type: "plain_text",
                  text: "🕐 종료 시간 변경",
                },
                value: JSON.stringify({
                  room: room,
                  date: date,
                  startTime: startTime,
                  userName: userName,
                  action: "modify_duration",
                }),
                action_id: "modify_meeting_duration_advanced",
              },
            ],
          },
        ],
        text: "회의 시간 수정",
      });

      // 시간 수정 메시지 삭제 (현재 메시지)
      if (actionBody.message?.ts) {
        try {
          await client.chat.delete({
            channel: actionBody.channel?.id || "",
            ts: actionBody.message.ts,
          });
        } catch (deleteError) {
          console.log(
            "Could not delete time modification message:",
            deleteError
          );
        }
      }

      // 원본 예약 메시지도 삭제 (있는 경우)
      if (originalMessageTs && originalMessageTs !== actionBody.message?.ts) {
        try {
          await client.chat.delete({
            channel: actionBody.channel?.id || "",
            ts: originalMessageTs,
          });
        } catch (deleteError) {
          console.log(
            "Could not delete original reservation message:",
            deleteError
          );
        }
      }
    } catch (error) {
      console.error("Error confirming advanced duration:", error);
      await client.chat.postMessage({
        channel: (body as any).channel?.id || "",
        text: "지속 시간 확정 처리 중 오류가 발생했습니다.",
      });
    }
  }
);

// 회의 시간 수정 버튼 핸들러 (기본)
app.action(
  "modify_meeting_duration",
  async ({ body, ack, respond, client }) => {
    await ack();

    try {
      const actionBody = body as {
        actions: Array<{ value: string }>;
        channel?: { id: string };
        message?: { ts: string };
        user?: { id: string };
      };

      const payload = JSON.parse(actionBody.actions[0].value);
      const { room, date, selectedTime, userName } = payload;

      // 선택한 시간에서 시작 시간을 파싱
      const [startHour, startMinute] = selectedTime.split(":").map(Number);

      // 실제 예약 가능한 시간 계산
      let maxAvailableMinutes = (22 - startHour) * 60 - startMinute; // 기본값: 22:00까지
      let durationOptions: Array<{ duration: number; label: string }> = [];
      try {
        // 구글 캘린더에서 해당 날짜의 이벤트들을 가져오기
        const calendarId = ROOM_CALENDARS[room];
        const events = await getRoomEvents(calendarId, date);

        // 실제 예약 가능한 최대 시간 계산 (시간 단위)
        const maxEndTime = getMaxAvailableTime(events, startHour, startMinute);

        // 실제 가능한 최대 시간(분)으로 계산
        maxAvailableMinutes = Math.min(
          maxAvailableMinutes,
          maxEndTime.hour * 60 +
            maxEndTime.minute -
            (startHour * 60 + startMinute)
        );
        // 30분 단위로 가능한 옵션들만 생성
        durationOptions = createDurationOptions(
          startHour,
          startMinute,
          maxEndTime
        );
      } catch (error) {
        console.error(
          "Error fetching calendar events for duration calculation:",
          error
        );
        // 오류 발생 시 기본값 사용
      }

      // 30분 미만 예약 불가 처리
      if (maxAvailableMinutes < 30 || durationOptions.length === 0) {
        await client.chat.postMessage({
          channel: actionBody.channel?.id || "",
          text: "선택하신 시간에는 예약 가능한 시간이 없습니다. 다른 시간을 선택해주세요.",
        });
        return;
      }

      // 첫 번째 옵션을 기본 선택으로 설정 (실제 가능한 최소 시간)
      const defaultDuration =
        durationOptions.find((opt) => opt.duration === 60)?.duration ||
        durationOptions[0]?.duration ||
        30;

      const blocks = [
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text: `:clock1: *${room}* 회의실 시간 수정\n\n📅 날짜: ${date}\n\n⏰ 시작 시간: ${selectedTime}\n\n회의 지속 시간을 선택해주세요:`,
          },
        },
        {
          type: "actions",
          elements: durationOptions
            .slice(0, 4)
            .map((option: { duration: number; label: string }) => ({
              type: "button",
              text: {
                type: "plain_text",
                text: option.label,
              },
              value: JSON.stringify({
                room: room,
                date: date,
                selectedTime: selectedTime,
                userName: userName,
                duration: option.duration,
                originalMessageTs: actionBody.message?.ts,
              }),
              action_id: `confirm_duration_${option.duration}`,
            })),
        },
      ];

      // 4개보다 많은 옵션이 있으면 두 번째 행 추가
      if (durationOptions.length > 4) {
        blocks.push({
          type: "actions",
          elements: durationOptions
            .slice(4)
            .map((option: { duration: number; label: string }) => ({
              type: "button",
              text: {
                type: "plain_text",
                text: option.label,
              },
              value: JSON.stringify({
                room: room,
                date: date,
                selectedTime: selectedTime,
                userName: userName,
                duration: option.duration,
                originalMessageTs: actionBody.message?.ts,
              }),
              action_id: `confirm_duration_${option.duration}`,
            })),
        });
      }

      await client.chat.postMessage({
        channel: actionBody.channel?.id || "",
        blocks: blocks,
        text: "회의 시간 수정",
      });
    } catch (error) {
      console.error("Error handling duration modification (통합):", error);
      await client.chat.postMessage({
        channel: (body as any).channel?.id || "",
        text: "시간 수정 처리 중 오류가 발생했습니다.",
      });
    }
  }
);

// 지속 시간 확정 핸들러 (기본)
app.action(/^confirm_duration_\d+$/, async ({ body, ack, respond, client }) => {
  await ack();

  try {
    const actionBody = body as {
      actions: Array<{ value: string }>;
      channel?: { id: string };
      message?: { ts: string };
    };

    const payload = JSON.parse(actionBody.actions[0].value);
    const { room, date, selectedTime, userName, duration, originalMessageTs } =
      payload;

    // 종료 시간 계산
    const [startHour, startMinute] = selectedTime.split(":").map(Number);
    const endTotalMinutes = startHour * 60 + startMinute + duration;
    const endHour = Math.floor(endTotalMinutes / 60);
    const endMin = endTotalMinutes % 60;
    const endTime = `${endHour.toString().padStart(2, "0")}:${endMin.toString().padStart(2, "0")}`;

    // 날짜와 시간을 올바르게 설정
    const startDate = new Date(date + "T00:00:00");
    startDate.setHours(startHour, startMinute, 0, 0);
    const endDate = new Date(startDate.getTime() + duration * 60 * 1000);

    // Google Calendar URL 생성 (회의실 리소스 포함)
    const calendarId = ROOM_CALENDARS[room];
    const calendarUrl = makeCalendarUrl({
      roomName: room,
      userName,
      startDate,
      endDate,
      calendarId,
    });

    // 지속 시간 표시를 위한 텍스트 생성
    const durationText = makeDurationText(duration);

    // 최종 예약 메시지 전송
    await client.chat.postMessage({
      channel: actionBody.channel?.id || "",
      blocks: [
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text: makeReservationMessage({
              roomName: room,
              userName,
              date,
              startTime: selectedTime,
              endTime: endTime,
              durationMinutes: duration,
            }),
            // text: `*${room}* 회의실 예약을 진행합니다.\n\n👤 주최자: ${userName}\n\n📅 ${date}\n\n⏰ ${selectedTime} ~ ${endTime} (${durationText})`,
          },
        },
        {
          type: "actions",
          elements: [
            {
              type: "button",
              text: {
                type: "plain_text",
                text: "Google Calendar에서 예약하기",
              },
              style: "primary",
              url: calendarUrl,
            },
            {
              type: "button",
              text: {
                type: "plain_text",
                text: "🕐 종료 시간 변경",
              },
              value: JSON.stringify({
                room: room,
                date: date,
                selectedTime: selectedTime,
                userName: userName,
                action: "modify_duration",
              }),
              action_id: "modify_meeting_duration",
            },
          ],
        },
      ],
      text: "회의실 예약",
    });

    // 시간 수정 메시지 삭제 (현재 메시지)
    if (actionBody.message?.ts) {
      try {
        await client.chat.delete({
          channel: actionBody.channel?.id || "",
          ts: actionBody.message.ts,
        });
      } catch (deleteError) {
        console.log("Could not delete time modification message:", deleteError);
      }
    }
  } catch (error) {
    console.error("Error handling duration confirmation:", error);
    await client.chat.postMessage({
      channel: (body as any).channel?.id || "",
      text: "시간 확정 처리 중 오류가 발생했습니다.",
    });
  }
});

// Focus Room 선택 처리
app.action(/^focus_room_selection_/, async ({ body, ack, respond, client }) => {
  await ack();

  try {
    const actionBody = body as {
      actions: Array<{ value: string; action_id: string }>;
      user?: { id: string };
      channel?: { id: string };
    };

    if (!actionBody.actions?.[0]?.value) {
      throw new Error("Invalid focus room selection payload");
    }

    const payload = JSON.parse(actionBody.actions[0].value);
    const { selectedRoom, originalRequest } = payload;

    // 사용자 정보 가져오기
    const userId = actionBody.user?.id;
    let userName = "사용자";

    if (userId) {
      try {
        const userInfo = await client.users.info({ user: userId });
        userName = userInfo.user?.real_name || userInfo.user?.name || "사용자";
      } catch (error) {
        console.error("Error fetching user info:", error);
      }
    }

    console.log("🎯 Focus Room 선택됨:", selectedRoom);
    console.log("📝 원본 요청:", originalRequest);

    // 🆕 [FOCUS] 로딩 메시지 삭제
    const channelId = actionBody.channel?.id;
    if (channelId && globalLoadingMessages.has(channelId)) {
      const focusLoadingMsg = globalLoadingMessages.get(channelId);
      try {
        console.log("🔄 [FOCUS] 로딩 메시지 삭제 시도:", focusLoadingMsg.ts);
        await client.chat.delete({
          channel: focusLoadingMsg.channel,
          ts: focusLoadingMsg.ts,
        });
        console.log("✅ [FOCUS] 로딩 메시지 삭제 성공:", focusLoadingMsg.ts);
        globalLoadingMessages.delete(channelId);
      } catch (error: any) {
        console.log("❌ [FOCUS] 로딩 메시지 삭제 실패:", error.message);
      }
    }

    // 🆕 로딩 메시지 표시
    const focusSelectLoadingId = Date.now();
    console.log(
      `🔄 [FOCUS_SELECT] 로딩 메시지 생성 시작... ID: ${focusSelectLoadingId}`
    );
    await respond({
      text: `:loading2: 더제로 봇이 예약 가능 여부를 확인 중입니다...`,
      replace_original: true,
    });
    console.log("🔄 [FOCUS_SELECT] 로딩 메시지 생성 완료:", {
      focusSelectLoadingId,
    });

    // 선택된 룸으로 추천 시스템 실행
    const dateStr =
      originalRequest.date || new Date().toISOString().split("T")[0];

    // 선택된 룸만 조회
    const roomData: {
      [roomName: string]: {
        events: {
          start: string;
          end: string;
          summary: string;
          attendees: any[];
        }[];
      };
    } = {};

    try {
      const calendarId = ROOM_CALENDARS[selectedRoom];
      const events = await getRoomEvents(calendarId, dateStr);
      roomData[selectedRoom] = {
        events: events.map((event) => ({
          ...event,
          attendees: [],
        })),
      };
    } catch (error) {
      console.error(`Error fetching events for ${selectedRoom}:`, error);
      roomData[selectedRoom] = { events: [] };
    }

    // 추천 시스템 실행
    const recommendation = await recommendAvailableRooms(
      roomData,
      dateStr,
      originalRequest.time || undefined,
      originalRequest.attendeeCount
    );

    if (recommendation) {
      const blocks = buildRecommendationBlocks(
        recommendation,
        actionBody.user?.id || "",
        actionBody.channel?.id || "",
        dateStr,
        userName,
        originalRequest.time || undefined,
        selectedRoom, // 🆕 다시 selectedRoom 사용하여 직접 예약 블록 생성
        originalRequest.duration || undefined
      );

      await respond({
        text: `✅ ${selectedRoom} 선택됨\n\n${recommendation.message}`,
        blocks: blocks,
        replace_original: true,
      });
    } else {
      await respond({
        text: `❌ ${selectedRoom}에 대한 정보를 가져올 수 없습니다.`,
        replace_original: true,
      });
    }
  } catch (error) {
    console.error("Error in focus room selection:", error);
    await respond({
      text: "포커스 룸 선택 처리 중 오류가 발생했습니다.",
      replace_original: true,
    });
  }
});

// 바로 예약 버튼 클릭 이벤트 처리
app.action("direct_reservation", async ({ body, ack, respond, client }) => {
  await ack();

  try {
    const actionBody = body as {
      actions: Array<{ value: string }>;
      user?: { id: string };
      channel?: { id: string };
    };

    const payload = JSON.parse(actionBody.actions[0].value);
    const { roomName, targetTime, date, availableSlot, durationMinutes } =
      payload;

    console.log("🔍 바로 예약 payload 분석:", {
      roomName,
      targetTime,
      date,
      durationMinutes,
      payloadKeys: Object.keys(payload),
    });

    // 사용자 정보 가져오기
    const userId = actionBody.user?.id;
    let userName = "사용자";

    if (userId) {
      try {
        const userInfo = await client.users.info({ user: userId });
        userName = userInfo.user?.real_name || userInfo.user?.name || "사용자";
      } catch (error) {
        console.error("Error fetching user info:", error);
      }
    }

    // 시간 파싱
    const [targetHour, targetMinute = 0] = targetTime.split(":").map(Number);

    // 🆕 사용자가 요청한 지속시간 사용 (payload에서 전달받은 값)
    const finalDurationMinutes = durationMinutes || 60; // 기본값 60분
    console.log(
      `🎯 바로 예약 - 사용자 요청 지속시간: ${finalDurationMinutes}분`
    );

    const startDate = new Date(date + "T00:00:00");
    startDate.setHours(targetHour, targetMinute, 0, 0);

    const calendarId = ROOM_CALENDARS[roomName];

    // 🆕 사용자 요청 지속시간으로 종료 시간 계산
    const endDate = new Date(
      startDate.getTime() + finalDurationMinutes * 60000
    );
    const endTime = `${endDate.getHours().toString().padStart(2, "0")}:${endDate.getMinutes().toString().padStart(2, "0")}`;

    console.log("🕐 시간 계산 결과:", {
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      startTime: targetTime,
      endTime: endTime,
      finalDurationMinutes: finalDurationMinutes,
    });

    const calendarUrl = makeCalendarUrl({
      roomName,
      userName,
      startDate,
      endDate,
      calendarId,
    });

    await respond({
      text: makeReservationMessage({
        roomName: roomName,
        userName,
        date,
        startTime: targetTime,
        endTime,
        durationMinutes: finalDurationMinutes,
      }),
      blocks: [
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text: makeReservationMessage({
              roomName: roomName,
              userName,
              date,
              startTime: targetTime,
              endTime,
              durationMinutes: finalDurationMinutes,
            }),
          },
        },
        {
          type: "actions",
          elements: [
            {
              type: "button",
              text: {
                type: "plain_text",
                text: "Google Calendar에서 예약하기",
              },
              style: "primary",
              url: calendarUrl,
            },
            {
              type: "button",
              text: {
                type: "plain_text",
                text: "🕐 종료 시간 변경",
              },
              value: JSON.stringify({
                room: roomName,
                date: date,
                selectedTime: targetTime,
                userName: userName,
                action: "modify_duration",
              }),
              action_id: "modify_meeting_duration",
            },
          ],
        },
      ],
      response_type: "ephemeral",
    });
  } catch (error) {
    console.error("Error in direct reservation handler:", error);
    await respond({
      text: "예약 처리 중 오류가 발생했습니다.",
      response_type: "ephemeral",
    });
  }
});

// === 시간 변경하기 버튼 액션 핸들러 추가 ===
app.action("change_time_direct", async ({ body, ack, respond, client }) => {
  await ack();
  try {
    const actionBody = body as {
      actions: Array<{ value: string }>;
      user?: { id: string };
      channel?: { id: string };
    };
    const payload = JSON.parse(actionBody.actions[0].value);
    const { room, date, userId } = payload;
    const channelId = (body as any).channel?.id;

    // 기존 예약 상태가 있으면 삭제
    const stateInfo = reservationStateManager.getStateByChannel(channelId);
    if (stateInfo) {
      const [stateId] = stateInfo;
      reservationStateManager.deleteState(stateId);
    }

    // 예약 상태 새로 생성 (시간 입력 단계로)
    const availableSlot = { start: "09:00", end: "22:00" };
    const stateId = reservationStateManager.createState(
      userId,
      channelId,
      room,
      date,
      availableSlot
    );

    await respond({
      text: "변경할 시작 시간을 입력해주세요. (예: 14:00, 15:30)",
      blocks: createTimeInputPromptBlocks(stateId, availableSlot),
      replace_original: true,
    });
  } catch (error) {
    console.error("Error handling change_time_direct action:", error);
    await respond({
      text: "시간 변경 처리 중 오류가 발생했습니다.",
      replace_original: false,
    });
  }
});
// ... 기존 코드 ...

app.event("app_home_opened", async ({ event, client }) => {
  try {
    // 오늘 날짜 (YYYY-MM-DD)
    const today = new Date();
    const yyyy = today.getFullYear();
    const mm = String(today.getMonth() + 1).padStart(2, "0");
    const dd = String(today.getDate()).padStart(2, "0");
    const todayStr = `${yyyy}-${mm}-${dd}`;

    await client.views.publish({
      user_id: event.user,
      view: {
        type: "home",
        blocks: [
          {
            type: "header",
            text: { type: "plain_text", text: "🤖 pxd 회의실 예약 더제로 봇" },
          },
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: "슬랙에서 회의실 예약, 추천, 현황 안내를 쉽고 빠르게 도와드립니다!",
            },
          },
          { type: "divider" },
          {
            type: "actions",
            elements: [
              {
                type: "datepicker",
                action_id: "home_datepicker",
                initial_date: todayStr,
                placeholder: { type: "plain_text", text: "날짜 선택" },
              },
              {
                type: "button",
                text: { type: "plain_text", text: "회의실 추천" },
                action_id: "home_recommend_by_date",
              },
              {
                type: "button",
                text: { type: "plain_text", text: "회의실 현황 보기" },
                action_id: "home_status_by_date",
              },
            ],
          },
          { type: "divider" },
          {
            type: "section",
            fields: [
              {
                type: "mrkdwn",
                text: "🗓️ *회의실 예약*",
              },
            ],
          },
          {
            type: "context",
            elements: [
              {
                type: "mrkdwn",
                text: "• 원하는 날짜/시간/회의실을 입력하면 바로 구글 캘린더 예약 버튼 제공\n• `내일 오후 2시 The1 예약해줘`",
              },
            ],
          },
          {
            type: "section",
            fields: [
              {
                type: "mrkdwn",
                text: "✨ *회의실 추천*",
              },
            ],
          },
          {
            type: "context",
            elements: [
              {
                type: "mrkdwn",
                text: "• 빈 회의실/시간대를 추천\n• `오늘 회의실 추천`",
              },
            ],
          },
          {
            type: "section",
            fields: [
              {
                type: "mrkdwn",
                text: "📋 *예약 현황*",
              },
            ],
          },
          {
            type: "context",
            elements: [
              {
                type: "mrkdwn",
                text: "• 전체/특정 회의실의 예약 현황 확인\n• `내일 회의실 현황 알려줘`",
              },
            ],
          },
          {
            type: "section",
            fields: [
              {
                type: "mrkdwn",
                text: "ℹ️ *회의실 정보*",
              },
            ],
          },
          {
            type: "context",
            elements: [
              {
                type: "mrkdwn",
                text: "• 각 회의실의 수용 인원 등 정보 안내\n• `회의실 정보`",
              },
            ],
          },
        ],
      },
    });
  } catch (error) {
    console.error(error);
  }
});

// 홈탭: 오늘 회의실 추천 버튼 액션 핸들러
app.action("home_recommend_today", async ({ body, ack, client }) => {
  await ack();
  try {
    const userId = body.user.id;
    // DM 채널 오픈
    const im = await client.conversations.open({ users: userId });
    const dmChannel = im.channel?.id;
    if (!dmChannel) return;
    // DM에 로딩 메시지 먼저 전송
    const loadingMsg = await client.chat.postMessage({
      channel: dmChannel,
      text: ":loading2: 더제로 봇이 답변을 입력 중입니다.",
    });
    // 오늘 날짜
    const today = new Date();
    const yyyy = today.getFullYear();
    const mm = String(today.getMonth() + 1).padStart(2, "0");
    const dd = String(today.getDate()).padStart(2, "0");
    const dateStr = `${yyyy}-${mm}-${dd}`;
    // 전체 회의실 이벤트 조회
    const roomData: any = {};
    for (const roomName of Object.keys(ROOM_CALENDARS) as string[]) {
      try {
        const events = await getRoomEvents(ROOM_CALENDARS[roomName], dateStr);
        roomData[roomName] = { events };
      } catch (e) {
        roomData[roomName] = { events: [] };
      }
    }
    // 추천 결과 생성
    const recommendation = await recommendAvailableRooms(roomData, dateStr);
    const blocks = buildRecommendationBlocks(
      recommendation,
      userId,
      dmChannel,
      dateStr
    );
    // 로딩 메시지 삭제
    if (loadingMsg && loadingMsg.ts) {
      await client.chat.delete({ channel: dmChannel, ts: loadingMsg.ts });
    }
    await client.chat.postMessage({
      channel: dmChannel,
      text: recommendation.message,
      blocks,
    });
  } catch (error) {
    console.error("Error in home_recommend_today action:", error);
  }
});

// 홈탭: 회의실 현황 보기 버튼 액션 핸들러
app.action("home_view_status", async ({ body, ack, client }) => {
  await ack();
  try {
    const userId = body.user.id;
    // DM 채널 오픈
    const im = await client.conversations.open({ users: userId });
    const dmChannel = im.channel?.id;
    if (!dmChannel) return;
    // DM에 로딩 메시지 먼저 전송
    const loadingMsg = await client.chat.postMessage({
      channel: dmChannel,
      text: ":loading2: 더제로 봇이 답변을 입력 중입니다.",
    });
    // 오늘 날짜
    const today = new Date();
    const yyyy = today.getFullYear();
    const mm = String(today.getMonth() + 1).padStart(2, "0");
    const dd = String(today.getDate()).padStart(2, "0");
    const dateStr = `${yyyy}-${mm}-${dd}`;
    // 전체 회의실 이벤트 조회
    const roomData: any = {};
    for (const roomName of Object.keys(ROOM_CALENDARS) as string[]) {
      try {
        const events = await getRoomEvents(ROOM_CALENDARS[roomName], dateStr);
        roomData[roomName] = { events };
      } catch (e) {
        roomData[roomName] = { events: [] };
      }
    }
    // 현황 Block Kit 생성
    const blocks = await getRoomReservationBlockKit(dateStr, roomData);
    // 로딩 메시지 삭제
    if (loadingMsg && loadingMsg.ts) {
      await client.chat.delete({ channel: dmChannel, ts: loadingMsg.ts });
    }
    await client.chat.postMessage({
      channel: dmChannel,
      text: `${dateStr} 회의실 예약 현황입니다.`,
      blocks,
    });
  } catch (error) {
    console.error("Error in home_view_status action:", error);
  }
});

// 홈탭: 날짜 기반 회의실 추천 버튼 액션 핸들러
app.action("home_recommend_by_date", async ({ body, ack, client }) => {
  await ack();
  try {
    const userId = body.user.id;
    // DM 채널 오픈
    const im = await client.conversations.open({ users: userId });
    const dmChannel = im.channel?.id;
    if (!dmChannel) return;
    // DM에 로딩 메시지 먼저 전송
    const loadingMsg = await client.chat.postMessage({
      channel: dmChannel,
      text: ":loading2: 더제로 봇이 답변을 입력 중입니다.",
    });
    // 날짜 추출 (없으면 오늘)
    let selectedDate = undefined;
    const stateValues =
      (body as any).view?.state?.values || (body as any).state?.values;
    if (stateValues) {
      for (const block of Object.values(stateValues)) {
        for (const action of Object.values(block as any)) {
          const act = action as any;
          if (act.type === "datepicker" && act.selected_date) {
            selectedDate = act.selected_date;
          }
        }
      }
    }
    if (!selectedDate) {
      const today = new Date();
      const yyyy = today.getFullYear();
      const mm = String(today.getMonth() + 1).padStart(2, "0");
      const dd = String(today.getDate()).padStart(2, "0");
      selectedDate = `${yyyy}-${mm}-${dd}`;
    }
    // 전체 회의실 이벤트 조회
    const roomData: any = {};
    for (const roomName of Object.keys(ROOM_CALENDARS) as string[]) {
      try {
        const events = await getRoomEvents(
          ROOM_CALENDARS[roomName],
          selectedDate
        );
        roomData[roomName] = { events };
      } catch (e) {
        roomData[roomName] = { events: [] };
      }
    }
    // 추천 결과 생성
    const recommendation = await recommendAvailableRooms(
      roomData,
      selectedDate
    );
    const blocks = buildRecommendationBlocks(
      recommendation,
      userId,
      dmChannel,
      selectedDate
    );
    // 로딩 메시지 삭제
    if (loadingMsg && loadingMsg.ts) {
      await client.chat.delete({ channel: dmChannel, ts: loadingMsg.ts });
    }
    await client.chat.postMessage({
      channel: dmChannel,
      text: recommendation.message,
      blocks,
    });
  } catch (error) {
    console.error("Error in home_recommend_by_date action:", error);
  }
});

// 홈탭: 날짜 기반 회의실 현황 보기 버튼 액션 핸들러
app.action("home_status_by_date", async ({ body, ack, client }) => {
  await ack();
  try {
    const userId = body.user.id;
    // DM 채널 오픈
    const im = await client.conversations.open({ users: userId });
    const dmChannel = im.channel?.id;
    if (!dmChannel) return;
    // DM에 로딩 메시지 먼저 전송
    const loadingMsg = await client.chat.postMessage({
      channel: dmChannel,
      text: ":loading2: 더제로 봇이 답변을 입력 중입니다.",
    });
    // 날짜 추출 (없으면 오늘)
    let selectedDate = undefined;
    const stateValues =
      (body as any).view?.state?.values || (body as any).state?.values;
    if (stateValues) {
      for (const block of Object.values(stateValues)) {
        for (const action of Object.values(block as any)) {
          const act = action as any;
          if (act.type === "datepicker" && act.selected_date) {
            selectedDate = act.selected_date;
          }
        }
      }
    }
    if (!selectedDate) {
      const today = new Date();
      const yyyy = today.getFullYear();
      const mm = String(today.getMonth() + 1).padStart(2, "0");
      const dd = String(today.getDate()).padStart(2, "0");
      selectedDate = `${yyyy}-${mm}-${dd}`;
    }
    // 전체 회의실 이벤트 조회
    const roomData: any = {};
    for (const roomName of Object.keys(ROOM_CALENDARS) as string[]) {
      try {
        const events = await getRoomEvents(
          ROOM_CALENDARS[roomName],
          selectedDate
        );
        roomData[roomName] = { events };
      } catch (e) {
        roomData[roomName] = { events: [] };
      }
    }
    // 현황 Block Kit 생성
    const blocks = await getRoomReservationBlockKit(selectedDate, roomData);
    // 로딩 메시지 삭제
    if (loadingMsg && loadingMsg.ts) {
      await client.chat.delete({ channel: dmChannel, ts: loadingMsg.ts });
    }
    await client.chat.postMessage({
      channel: dmChannel,
      text: `${selectedDate} 회의실 예약 현황입니다.`,
      blocks,
    });
  } catch (error) {
    console.error("Error in home_status_by_date action:", error);
  }
});

(async () => {
  await app.start();
  console.log("⚡️ Bolt app is running!");
})();
