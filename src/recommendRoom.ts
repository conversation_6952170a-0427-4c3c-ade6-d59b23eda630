import { WebClient } from "@slack/web-api";
import "dotenv/config";
import { KnownBlock } from "@slack/types";
import {
  ROOM_CALENDARS,
  ROOM_CAPACITY,
  getRoomsByCapacity,
} from "./meetingRooms";
import { reservationStateManager } from "./reservationState";
import { isRoomAvailable, getMaxAvailableTime } from "./roomAvailability";
import {
  makeReservationMessage,
  makeRoomIcon,
  makeCalendarUrl,
  validateDurationAndCreateErrorMessage,
  makeDurationText,
} from "./utils/reservationUtils";

const slackClient = new WebClient(process.env.SLACK_BOT_TOKEN);

interface TimeSlot {
  start: Date;
  end: Date;
}

interface RoomAvailability {
  roomName: string;
  availableSlots: TimeSlot[];
}

interface RecommendationResult {
  availableRooms: RoomAvailability[];
  message: string;
}

// 시간 포맷: HH:MM
function formatTime(date: Date) {
  const hh = String(date.getHours()).padStart(2, "0");
  const min = String(date.getMinutes()).padStart(2, "0");
  return `${hh}:${min}`;
}

// 날짜 포맷: YYYY-MM-DD
function formatDate(date: Date) {
  const yyyy = date.getFullYear();
  const mm = String(date.getMonth() + 1).padStart(2, "0");
  const dd = String(date.getDate()).padStart(2, "0");
  return `${yyyy}-${mm}-${dd}`;
}

// 한글 날짜 포맷: YYYY년 M월 D일 (요일)
function formatDateKorean(date: Date) {
  const weekdays = ["일", "월", "화", "수", "목", "금", "토"];
  const yyyy = date.getFullYear();
  const mm = date.getMonth() + 1;
  const dd = date.getDate();
  const weekday = weekdays[date.getDay()];
  return `${yyyy}년 ${mm}월 ${dd}일 (${weekday})`;
}

// 시간 문자열을 Date 객체로 변환 (HH:MM 형식)
function parseTimeStr(timeStr: string, baseDate: Date): Date {
  const [hours, minutes] = timeStr.split(":").map(Number);
  const date = new Date(baseDate);
  date.setHours(hours, minutes, 0, 0);
  return date;
}

// 연속된 시간대 병합
function mergeConsecutiveSlots(slots: TimeSlot[]): TimeSlot[] {
  if (slots.length === 0) return [];

  const merged: TimeSlot[] = [];
  let current = { ...slots[0] };

  for (let i = 1; i < slots.length; i++) {
    const next = slots[i];
    // 현재 슬롯의 종료 시간이 다음 슬롯의 시작 시간과 같으면 병합
    if (current.end.getTime() === next.start.getTime()) {
      current.end = next.end;
    } else {
      merged.push(current);
      current = { ...next };
    }
  }
  merged.push(current);

  return merged;
}

// 빈 시간대 찾기
function findAvailableSlots(events: any[], targetDate: Date): TimeSlot[] {
  // 종일 예약 체크 (00:00부터 00:00까지인 경우)
  const hasAllDayReservation = events.some(
    (event) => event.start === "00:00" && event.end === "00:00"
  );

  if (hasAllDayReservation) {
    console.log(
      "Room has all-day reservation (00:00-00:00), no available slots."
    );
    return [];
  }

  const slots: TimeSlot[] = [];
  const dayStart = new Date(targetDate);
  dayStart.setHours(9, 0, 0, 0); // 업무 시작 시간 9:00
  const dayEnd = new Date(targetDate);
  dayEnd.setHours(22, 0, 0, 0); // 업무 종료 시간 22:00

  // 현재 시간
  const now = new Date();

  // 시작 시간 설정
  let startTime: Date;
  if (targetDate.toDateString() === now.toDateString()) {
    // 오늘인 경우 현재 시간 이후의 가장 가까운 30분 단위로 설정
    startTime = new Date(
      Math.ceil(now.getTime() / (30 * 60 * 1000)) * (30 * 60 * 1000)
    );
  } else if (targetDate > now) {
    // 미래 날짜인 경우 해당 일의 업무 시작 시간으로 설정
    startTime = dayStart;
  } else {
    // 과거 날짜인 경우 빈 배열 반환
    return [];
  }

  // 이벤트 시간 파싱 및 정렬
  const sortedEvents = events
    .map((event) => ({
      start: parseTimeStr(event.start, targetDate),
      end: parseTimeStr(event.end, targetDate),
      summary: event.summary,
    }))
    .sort((a, b) => a.start.getTime() - b.start.getTime());

  console.log(
    "Sorted events:",
    sortedEvents.map((e) => ({
      summary: e.summary,
      start: formatTime(e.start),
      end: formatTime(e.end),
    }))
  );

  let currentTime = startTime;
  let eventIndex = 0;

  while (currentTime < dayEnd && eventIndex < sortedEvents.length) {
    const event = sortedEvents[eventIndex];

    // 현재 시간이 이벤트 시작 시간보다 이전이면 빈 슬롯 추가
    if (currentTime < event.start) {
      slots.push({
        start: currentTime,
        end: new Date(Math.min(event.start.getTime(), dayEnd.getTime())),
      });
    }

    // 현재 시간을 이벤트 종료 시간으로 이동
    currentTime = new Date(
      Math.max(currentTime.getTime(), event.end.getTime())
    );
    eventIndex++;
  }

  // 마지막 이벤트 이후 시간 처리
  if (currentTime < dayEnd) {
    slots.push({
      start: currentTime,
      end: dayEnd,
    });
  }

  // 찾은 슬롯들을 병합
  return mergeConsecutiveSlots(slots);
}

export async function recommendAvailableRooms(
  roomData: {
    [roomName: string]: {
      events: {
        start: string;
        end: string;
        summary: string;
        attendees: { email: string; displayName?: string }[];
      }[];
    };
  },
  targetDate?: string,
  targetTime?: string, // 특정 시간 추가 (HH:MM 형식)
  attendeeCount?: number // 참석 인원수 추가
): Promise<RecommendationResult> {
  // 날짜 설정
  let date = targetDate ? new Date(targetDate) : new Date();
  // 날짜가 유효하지 않으면 현재 날짜 사용
  if (isNaN(date.getTime())) {
    date = new Date();
  }

  // 참석 인원수에 따른 적합한 회의실 목록 생성
  let suitableRooms: string[] = [];
  if (attendeeCount && attendeeCount > 0) {
    suitableRooms = getRoomsByCapacity(attendeeCount);
    console.log(
      `👥 ${attendeeCount}명에 적합한 회의실: ${suitableRooms.join(", ")}`
    );
  } else {
    suitableRooms = Object.keys(roomData); // 모든 회의실
  }

  const availableRooms: RoomAvailability[] = [];

  // 특정 시간이 지정된 경우
  if (targetTime) {
    console.log(`\n=== 특정 시간(${targetTime}) 예약 가능 회의실 검색 ===`);
    const [targetHour, targetMinute] = targetTime.split(":").map(Number);

    for (const [roomName, data] of Object.entries(roomData)) {
      // 참석 인원수 조건 확인
      if (attendeeCount && !suitableRooms.includes(roomName)) {
        console.log(
          `❌ ${roomName}: 수용 인원 부족 (${ROOM_CAPACITY[roomName]}명 < ${attendeeCount}명)`
        );
        continue;
      }

      console.log(`\n회의실 ${roomName} 체크 중...`);

      // 🆕 isRoomAvailable 함수를 사용하여 특정 시간에 예약 가능한지 확인 (분 단위 포함)
      const isAvailable = isRoomAvailable(
        data.events,
        targetHour,
        roomName,
        targetMinute
      );

      if (isAvailable) {
        // 해당 시간부터 예약 가능한 시간대 계산
        const availableSlots = findAvailableSlots(data.events, date).filter(
          (slot) => {
            const slotStartHour = slot.start.getHours();
            const slotEndHour = slot.end.getHours();
            // 지정된 시간이 이 슬롯에 포함되는지 확인
            return slotStartHour <= targetHour && slotEndHour > targetHour;
          }
        );

        if (availableSlots.length > 0) {
          availableRooms.push({
            roomName,
            availableSlots,
          });
        }
      }
    }

    // 특정 시간에 대한 결과 메시지 생성
    const dateStr = formatDateKorean(date);
    const attendeeInfo = attendeeCount ? ` (${attendeeCount}명)` : "";

    if (availableRooms.length === 0) {
      const noRoomMessage =
        attendeeCount && suitableRooms.length === 0
          ? `${attendeeCount}명이 수용 가능한 회의실이 없습니다.`
          : `${targetTime}에 예약 가능한 회의실이 없습니다.`;

      return {
        availableRooms: [],
        message: `[📌 회의실 추천 - ${dateStr} ${targetTime}시 ${attendeeInfo}]\n  - ${noRoomMessage}`,
      };
    }

    const recommendations = availableRooms
      .map((room) => {
        const capacity = ROOM_CAPACITY[room.roomName];
        const capacityInfo = capacity ? ` (최대 ${capacity}명)` : "";
        return `[${room.roomName}${capacityInfo}]\n  - ${targetTime}부터 예약 가능`;
      })
      .join("\n\n");

    return {
      availableRooms,
      message: `[📌 회의실 추천 - ${dateStr} ${targetTime}시 ${attendeeInfo}]\n${recommendations}`,
    };
  }

  // 특정 시간이 지정되지 않은 경우 (기존 로직)
  for (const [roomName, data] of Object.entries(roomData)) {
    // 참석 인원수 조건 확인
    if (attendeeCount && !suitableRooms.includes(roomName)) {
      console.log(
        `❌ ${roomName}: 수용 인원 부족 (${ROOM_CAPACITY[roomName]}명 < ${attendeeCount}명)`
      );
      continue;
    }

    console.log(`\nProcessing room: ${roomName}`);
    console.log("Events:", data.events);

    const availableSlots = findAvailableSlots(data.events, date);
    console.log(
      "Available slots:",
      availableSlots.map((slot) => ({
        start: formatTime(slot.start),
        end: formatTime(slot.end),
      }))
    );

    if (availableSlots.length > 0) {
      availableRooms.push({
        roomName,
        availableSlots,
      });
    }
  }

  // 날짜 문자열 생성
  const dateStr = formatDateKorean(date);
  const attendeeInfo = attendeeCount ? ` (${attendeeCount}명)` : "";

  // 사용 가능한 회의실이 없는 경우
  if (availableRooms.length === 0) {
    const noRoomMessage =
      attendeeCount && suitableRooms.length === 0
        ? `${attendeeCount}명이 수용 가능한 회의실이 없습니다.`
        : "예약 가능한 회의실이 없습니다.";

    return {
      availableRooms: [],
      message: `[📌 회의실 추천 - ${dateStr}${attendeeInfo}]\n  - ${noRoomMessage}`,
    };
  }

  // 메시지 생성
  const recommendations = availableRooms
    .map((room) => {
      const capacity = ROOM_CAPACITY[room.roomName];
      const capacityInfo = capacity ? ` (최대 ${capacity}명)` : "";
      const slots = room.availableSlots
        .map(
          (slot) =>
            `  - ${formatTime(slot.start)}~${formatTime(slot.end)} : 예약 가능`
        )
        .join("\n");

      return `[${room.roomName}${capacityInfo}]\n${slots}`;
    })
    .join("\n\n");

  return {
    availableRooms,
    message: `[📌 회의실 추천 - ${dateStr}${attendeeInfo}]\n${recommendations}`,
  };
}

// 특정 시간과 회의실이 명확히 지정된 경우를 위한 직접 예약 블록 생성
export function buildDirectReservationBlocks(
  roomName: string,
  targetTime: string,
  date: string,
  isAvailable: boolean,
  availableSlots: TimeSlot[],
  userId?: string,
  channelId?: string,
  userName?: string,
  requestedDuration?: number
): KnownBlock[] {
  const blocks: KnownBlock[] = [];
  const dateStr = formatDateKorean(new Date(date));

  // 🆕 상단 헤더와 구분선 제거 (일반 회의실과 동일한 양식)

  if (!isAvailable || availableSlots.length === 0) {
    // 🆕 예약 불가 시에도 동일한 형식으로 표시
    const endTime = requestedDuration
      ? `${(parseInt(targetTime.split(":")[0]) + Math.floor(requestedDuration / 60)).toString().padStart(2, "0")}:${((parseInt(targetTime.split(":")[1]) || 0) + (requestedDuration % 60)).toString().padStart(2, "0")}`
      : `${(parseInt(targetTime.split(":")[0]) + 1).toString().padStart(2, "0")}:${targetTime.split(":")[1] || "00"}`;

    blocks.push({
      type: "section",
      text: {
        type: "mrkdwn",
        text: makeReservationMessage({
          roomName: roomName,
          userName: userName || "사용자",
          date,
          startTime: targetTime,
          endTime,
          durationMinutes: requestedDuration || 60,
          isUnavailable: true, // 🆕 예약 불가 표시
        }),
      },
    });
    return blocks;
  }

  // 정확한 예약 가능 시간 계산
  let durationMinutes = 60;
  let slot: TimeSlot | undefined = undefined;

  // targetTime을 시간과 분으로 파싱
  const [targetHour, targetMinute] = targetTime.split(":").map(Number);

  // 해당 날짜의 이벤트들을 문자열 형태로 변환
  const eventStrings = availableSlots.length > 0 ? [] : []; // 임시로 빈 배열, 실제로는 events가 필요

  // 실제 예약 가능한 최대 시간을 정확히 계산하기 위해 더 정교한 로직 필요
  if (availableSlots.length > 0) {
    // 요청한 시간이 포함된 슬롯 찾기
    slot = availableSlots.find((timeSlot) => {
      const slotStartHour = timeSlot.start.getHours();
      const slotStartMinute = timeSlot.start.getMinutes();
      const slotEndHour = timeSlot.end.getHours();
      const slotEndMinute = timeSlot.end.getMinutes();

      const slotStartMinutes = slotStartHour * 60 + slotStartMinute;
      const slotEndMinutes = slotEndHour * 60 + slotEndMinute;
      const targetMinutes = targetHour * 60 + targetMinute;

      return (
        targetMinutes >= slotStartMinutes && targetMinutes < slotEndMinutes
      );
    });

    if (slot) {
      // 요청한 시간부터 슬롯 끝까지의 시간 계산
      const slotEndMinutes = slot.end.getHours() * 60 + slot.end.getMinutes();
      const targetMinutes = targetHour * 60 + targetMinute;
      const maxAvailableMinutes = slotEndMinutes - targetMinutes;

      console.log("🔍 buildDirectReservationBlocks 지속시간 계산:", {
        requestedDuration,
        maxAvailableMinutes,
        roomName,
        targetTime,
      });

      if (requestedDuration) {
        // 다음 예약 시간 계산
        const nextReservationTime = slot
          ? `${slot.end.getHours()}:${slot.end.getMinutes().toString().padStart(2, "0")}`
          : "정보 없음";

        // 지속시간 검증 및 에러 메시지 생성
        const validation = validateDurationAndCreateErrorMessage({
          requestedDuration,
          maxAvailableMinutes,
          roomName,
          date,
          startTime: targetTime,
          nextReservationTime,
        });

        if (!validation.isValid) {
          console.log(
            `❌ 지속시간 초과: ${requestedDuration}분 > ${maxAvailableMinutes}분`
          );

          blocks.push({
            type: "section",
            text: {
              type: "mrkdwn",
              text: validation.errorMessage!,
            },
          });

          return blocks;
        }

        // 사용자가 지속시간을 명시한 경우
        durationMinutes = requestedDuration;
        console.log(`✅ 사용자 요청 지속시간 사용: ${durationMinutes}분`);
      } else {
        // 기본 지속시간 로직
        durationMinutes =
          maxAvailableMinutes >= 60 ? 60 : maxAvailableMinutes >= 30 ? 30 : 0;
        console.log(`📝 기본 지속시간 사용: ${durationMinutes}분`);
      }
    }
  }
  // 🆕 targetTime을 기준으로 정확한 종료 시간 계산
  const startTimeDate = new Date();
  startTimeDate.setHours(targetHour, targetMinute, 0, 0);
  const endTimeDate = new Date(
    startTimeDate.getTime() + durationMinutes * 60000
  );
  const endTime = `${endTimeDate.getHours().toString().padStart(2, "0")}:${endTimeDate.getMinutes().toString().padStart(2, "0")}`;

  console.log("🕐 buildDirectReservationBlocks 시간 계산:", {
    targetTime,
    durationMinutes,
    endTime,
    startTimeDate: startTimeDate.toTimeString(),
    endTimeDate: endTimeDate.toTimeString(),
  });
  blocks.push({
    type: "section",
    text: {
      type: "mrkdwn",
      text: makeReservationMessage({
        roomName: roomName,
        userName: userName || "사용자",
        date,
        startTime: targetTime,
        endTime,
        durationMinutes: durationMinutes,
      }),
      // text: `*${roomName}* 회의실 예약을 진행합니다.\n\n📅 ${date}\n\n⏰ ${targetTime} ~ ${endTime} (${durationMinutes}분)`,
    },
  });

  // 🆕 예약 가능 시간 정보 제거 (일반 회의실과 동일한 양식)

  // 바로 예약 버튼 (사용자 정보가 있는 경우에만)
  if (userId && channelId) {
    blocks.push({
      type: "actions",
      elements: [
        {
          type: "button" as const,
          text: {
            type: "plain_text" as const,
            text: "Google Calendar에서 예약하기",
            emoji: true,
          },
          // 🆕 Google Calendar URL 직접 생성
          url: makeCalendarUrl({
            roomName: roomName,
            userName: userName || "사용자",
            startDate: new Date(`${date}T${targetTime}`),
            endDate: new Date(
              new Date(`${date}T${targetTime}`).getTime() +
                (durationMinutes || 60) * 60000
            ),
            calendarId: ROOM_CALENDARS[roomName],
          }),
          action_id: "open_calendar",
          style: "primary" as const,
        },
        {
          type: "button" as const,
          text: {
            type: "plain_text" as const,
            text: "🕐 종료 시간 변경",
            emoji: true,
          },
          value: JSON.stringify({
            action: "custom_time_input",
            roomName: roomName,
            availableSlots: availableSlots.map((slot) => ({
              start: formatTime(slot.start),
              end: formatTime(slot.end),
            })),
            date: date,
            stateId: reservationStateManager.createState(
              userId,
              channelId,
              roomName,
              date,
              {
                start: targetTime,
                end: targetTime,
              }
            ),
          }),
          action_id: "custom_time_input",
        },
      ],
    });
  }

  return blocks;
}

// Block Kit 형식으로 추천 결과 변환
export function buildRecommendationBlocks(
  result: RecommendationResult,
  userId?: string,
  channelId?: string,
  date?: string,
  userName?: string,
  targetTime?: string,
  specificRoom?: string,
  requestedDuration?: number
): KnownBlock[] {
  // 특정 시간과 회의실이 모두 지정된 경우 직접 예약 블록 사용
  if (targetTime && specificRoom && result.availableRooms.length <= 1) {
    const room = result.availableRooms.find((r) => r.roomName === specificRoom);
    if (room) {
      return buildDirectReservationBlocks(
        specificRoom,
        targetTime,
        date || new Date().toISOString().split("T")[0],
        true,
        room.availableSlots,
        userId,
        channelId,
        userName,
        requestedDuration
      );
    } else {
      // 해당 회의실이 예약 불가능한 경우
      return buildDirectReservationBlocks(
        specificRoom,
        targetTime,
        date || new Date().toISOString().split("T")[0],
        false,
        [],
        userId,
        channelId,
        userName,
        requestedDuration
      );
    }
  }

  // 기존 로직 (일반적인 추천 시스템)
  const blocks: KnownBlock[] = [];
  const dateStr = result.message.split(" - ")[1].split("]\n")[0]; // 메시지에서 날짜 추출

  // 상단 헤더
  blocks.push({
    type: "section",
    text: {
      type: "mrkdwn",
      text: `:calendar: *회의실 추천* (*${dateStr}*)`,
    },
  });

  blocks.push({ type: "divider" });

  if (result.availableRooms.length === 0) {
    blocks.push({
      type: "section",
      text: {
        type: "mrkdwn",
        text: "현재 예약 가능한 회의실이 없습니다.",
      },
    });
    return blocks;
  }

  // 제한 없이 모든 회의실 표시
  for (const room of result.availableRooms) {
    blocks.push({
      type: "section",
      text: {
        type: "mrkdwn",
        text: `${makeRoomIcon(room.roomName)} *${room.roomName}*`,
      },
    });

    // 제한 없이 모든 사용 가능 시간대 표시
    for (const slot of room.availableSlots) {
      blocks.push({
        type: "context",
        elements: [
          {
            type: "mrkdwn",
            text: `• \`${formatTime(slot.start)}~${formatTime(slot.end)}\` - 예약 가능`,
          },
        ],
      });
    }

    // 예약 버튼 (사용자 정보가 있는 경우에만)
    if (userId && channelId && date) {
      blocks.push({
        type: "actions",
        elements: [
          {
            type: "button" as const,
            text: {
              type: "plain_text" as const,
              text: "이 회의실 예약하기",
              emoji: true,
            },
            value: JSON.stringify({
              action: "custom_time_input",
              roomName: room.roomName,
              availableSlots: room.availableSlots.map((slot) => ({
                start: formatTime(slot.start),
                end: formatTime(slot.end),
              })),
              date: date,
              stateId: reservationStateManager.createState(
                userId,
                channelId,
                room.roomName,
                date,
                {
                  start: formatTime(room.availableSlots[0].start),
                  end: formatTime(room.availableSlots[0].end),
                }
              ),
            }),
            action_id: "custom_time_input",
            style: "primary" as const,
          },
        ],
      });
    }

    blocks.push({ type: "divider" });
  }

  return blocks;
}
