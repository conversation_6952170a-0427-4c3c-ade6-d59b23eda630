import { makeRoomIcon } from "./utils/reservationUtils";

export const ROOM_CALENDARS: { [key: string]: string } = {
  The1: "<EMAIL>",
  The2: "<EMAIL>",
  The3: "<EMAIL>",
  The4: "<EMAIL>",
  "Light Room": "<EMAIL>",
  "Dark Room": "<EMAIL>",
  "Focus Room (East)":
    "<EMAIL>",
  "Focus Room (West)":
    "<EMAIL>",
};

// 회의실별 수용 인원 정보
export const ROOM_CAPACITY: { [key: string]: number } = {
  The1: 25, // 25명까지 수용 가능
  The2: 6, // 6명까지 수용 가능
  The3: 6, // 6명까지 수용 가능
  The4: 4, // 4명까지 수용 가능
  "Light Room": 6, // 6명까지 수용 가능 (소형 미팅룸)
  "Dark Room": 6, // 6명까지 수용 가능 (소형 미팅룸)
  "Focus Room (East)": 1, // 1명까지 수용 가능
  "Focus Room (West)": 1, // 1명까지 수용 가능
};

// 회의실 정보를 통합하여 제공하는 함수
export function getRoomInfo(roomName: string) {
  return {
    calendarId: ROOM_CALENDARS[roomName],
    capacity: ROOM_CAPACITY[roomName] || 0,
    name: roomName,
  };
}

// 모든 회의실 정보를 Block Kit 형식으로 반환하는 함수
export function buildRoomInfoBlocks() {
  const blocks: any[] = [];

  // 상단 헤더
  blocks.push({
    type: "section",
    text: {
      type: "mrkdwn",
      text: `:office: *전체 회의실 정보*`,
    },
  });

  blocks.push({ type: "divider" });

  // 각 회의실별 정보 표시
  for (const [roomName, capacity] of Object.entries(ROOM_CAPACITY)) {
    // 회의실명과 수용 인원 표시
    blocks.push({
      type: "section",
      text: {
        type: "mrkdwn",
        text: `${makeRoomIcon(roomName)} *${roomName}*`,
      },
    });

    blocks.push({
      type: "context",
      elements: [
        {
          type: "mrkdwn",
          text: `• 수용 인원: *${capacity}명*`,
        },
      ],
    });

    blocks.push({ type: "divider" });
  }

  return blocks;
}

// 특정 인원수에 적합한 회의실 목록 반환
export function getRoomsByCapacity(attendeeCount: number): string[] {
  return Object.entries(ROOM_CAPACITY)
    .filter(([, capacity]) => capacity >= attendeeCount)
    .map(([roomName]) => roomName)
    .sort((a, b) => ROOM_CAPACITY[a] - ROOM_CAPACITY[b]); // 수용 인원 순으로 정렬
}
