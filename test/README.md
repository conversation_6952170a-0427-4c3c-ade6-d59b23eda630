# Gemini API Rate Limiter Test

Bottleneck 라이브러리를 사용한 Gemini API 다중 모델 rate limiting 테스트 코드입니다.

## 📋 모델별 제한사항

| 모델 | 분당 제한 | 하루 제한 | 우선순위 |
|------|-----------|-----------|----------|
| gemini-2.0-flash | 10회 | 250회 | 1 (낮음) |
| gemini-1.5-pro | 15회 | 1000회 | 4 (높음) |
| gemini-1.5-flash | 15회 | 200회 | 2 |
| gemini-1.0-pro | 30회 | 200회 | 3 |

## 🚀 설치 및 실행

### 1. 의존성 설치
```bash
cd test
npm install bottleneck node-fetch
```

### 2. 기본 테스트 실행
```bash
# 기본 rate limiter 테스트
npm test

# 또는
node gemini-rate-limiter.test.js
```

### 3. 통합 테스트 실행 (실제 API 호출)
```bash
# 환경변수 설정
export GEMINI_API_KEY="your-actual-api-key"

# 통합 테스트 실행
npm run test:integration

# 또는
node gemini-integration-example.js
```

### 4. 스트레스 테스트 실행
```bash
# 가벼운 테스트 (10 요청, 3 동시)
node stress-test.js light

# 중간 테스트 (30 요청, 5 동시)
node stress-test.js medium

# 무거운 테스트 (100 요청, 10 동시)
node stress-test.js heavy

# 모든 테스트 순차 실행
node stress-test.js all

# 커스텀 테스트 (50 요청, 8 동시)
node stress-test.js custom 50 8
```

## 📁 파일 구조

```
test/
├── README.md                     # 이 파일
├── package.json                  # 의존성 설정
├── gemini-rate-limiter.test.js   # 기본 rate limiter 테스트 (시뮬레이션)
├── gemini-integration-example.js # 실제 API 통합 예시
└── stress-test.js               # 스트레스 테스트
```

## 🧪 테스트 종류

### 1. 기본 테스트 (`gemini-rate-limiter.test.js`)
- API 호출 시뮬레이션
- Rate limiting 동작 확인
- 모델 폴백 테스트
- 특정 모델 지정 테스트

### 2. 통합 테스트 (`gemini-integration-example.js`)
- 실제 Gemini API 호출
- nlpHandler.ts와 유사한 인터페이스
- 의도 분류 기능 테스트
- 실제 응답 처리

### 3. 스트레스 테스트 (`stress-test.js`)
- 대량 요청 처리
- 동시성 테스트
- 모델별 사용량 분석
- 성능 메트릭 수집

## 🔧 주요 기능

### Rate Limiting
- **분당 제한**: Bottleneck의 reservoir 기능 사용
- **하루 제한**: 별도 카운터로 관리
- **자동 리셋**: 매일 자정에 카운터 초기화

### 모델 선택 전략
1. **우선순위 기반**: 하루 제한이 높은 모델 우선 사용
2. **가용성 확인**: 분당/하루 제한 모두 체크
3. **자동 폴백**: 제한 도달 시 다른 모델로 자동 전환

### 에러 처리
- **Quota 초과**: 자동으로 다른 모델 시도
- **API 에러**: 상세한 에러 메시지 제공
- **재시도 로직**: 일시적 오류에 대한 재시도

## 📊 출력 예시

### 기본 테스트 출력
```
🧪 Gemini Rate Limiter Tests Starting...

=== 기본 사용 테스트 ===
✅ gemini-model-2: Available (reservoir: 15, daily: 0/1000)
🚀 Calling gemini-model-2 (daily usage: 1/1000)
✅ API Call Success: {
  model: 'gemini-model-2',
  result: { response: 'Response from gemini-model-2...', timestamp: '...' },
  dailyUsage: 1,
  dailyLimit: 1000
}
```

### 스트레스 테스트 출력
```
🏁 STRESS TEST RESULTS
============================================================
📊 Summary:
   Total Requests: 50
   Successful: 47
   Failed: 3
   Success Rate: 94.00%
   Duration: 12.34s
   Requests/sec: 4.05

🤖 Model Usage:
   gemini-model-2:
     Requests: 25
     Avg Response Time: 850ms
     Usage %: 53.2%
   gemini-model-4:
     Requests: 22
     Avg Response Time: 720ms
     Usage %: 46.8%
```

## 🔗 실제 적용 방법

### nlpHandler.ts에 적용
```typescript
import { GeminiAPIManager } from './gemini-api-manager';

const geminiManager = new GeminiAPIManager(process.env.GEMINI_API_KEY);

export async function getGeminiIntentClassification(message: string) {
  try {
    return await geminiManager.getGeminiIntentClassification(message, {
      preferredModel: '1.5-pro', // 높은 정확도가 필요한 경우
      temperature: 0.3
    });
  } catch (error) {
    console.error('Intent classification failed:', error);
    throw error;
  }
}
```

### 환경변수 설정
```bash
# .env 파일에 추가
GEMINI_API_KEY=your_actual_gemini_api_key_here
```

## 🚨 주의사항

1. **API 키 보안**: 실제 API 키를 코드에 하드코딩하지 마세요
2. **Rate Limit**: 실제 API 호출 시 Google의 rate limit 정책을 확인하세요
3. **비용 관리**: 하루 제한을 적절히 설정하여 비용을 관리하세요
4. **에러 처리**: 프로덕션에서는 더 robust한 에러 처리가 필요합니다

## 📈 성능 최적화 팁

1. **모델 선택**: 작업에 따라 적절한 모델 선택
2. **배치 처리**: 가능한 경우 요청을 배치로 처리
3. **캐싱**: 동일한 요청에 대한 응답 캐싱 고려
4. **모니터링**: 사용량과 성능 지표 모니터링

## 🤝 기여

버그 리포트나 개선 제안은 언제든 환영합니다!
