/**
 * Gemini API Integration Example
 * 실제 nlpHandler.ts에 적용할 수 있는 예시 코드
 */

const Bottleneck = require("bottleneck");
const fetch = require("node-fetch"); // 또는 axios

// 실제 Gemini API 호출을 위한 클래스
class GeminiAPIManager {
  constructor(apiKey) {
    this.apiKey = apiKey;
    this.limiters = {};
    this.dailyCounters = {};
    this.lastResetDate = new Date().toDateString();

    // 모델별 설정
    this.modelConfigs = {
      "2.5-flash": {
        perMinute: 10,
        perDay: 250,
        priority: 1,
        fullName: "gemini-2.5-flash",
      },
      "2.5-flash-lite-preview-06-17": {
        perMinute: 15,
        perDay: 1000,
        priority: 4,
        fullName: "gemini-2.5-flash-lite-preview-06-17",
      },
      "2.0-flash": {
        perMinute: 15,
        perDay: 200,
        priority: 2,
        fullName: "gemini-2.0-flash",
      },
      "2.0-flash-lite": {
        perMinute: 30,
        perDay: 200,
        priority: 3,
        fullName: "2.0-flash-lite",
      },
    };

    this.initializeLimiters();
  }

  initializeLimiters() {
    Object.keys(this.modelConfigs).forEach((modelKey) => {
      const config = this.modelConfigs[modelKey];

      this.limiters[modelKey] = new Bottleneck({
        reservoir: config.perMinute,
        reservoirRefreshAmount: config.perMinute,
        reservoirRefreshInterval: 60 * 1000,
        maxConcurrent: 1,
        minTime: Math.floor(60000 / config.perMinute),
        id: `gemini-${modelKey}`,
      });

      this.dailyCounters[modelKey] = 0;
    });
  }

  checkDailyReset() {
    const today = new Date().toDateString();
    if (today !== this.lastResetDate) {
      console.log("🔄 Daily API counters reset");
      Object.keys(this.dailyCounters).forEach((model) => {
        this.dailyCounters[model] = 0;
      });
      this.lastResetDate = today;
    }
  }

  getAvailableModel(preferredModel = null) {
    this.checkDailyReset();

    // 특정 모델이 요청된 경우 먼저 확인
    if (preferredModel && this.modelConfigs[preferredModel]) {
      if (this.isModelAvailable(preferredModel)) {
        return preferredModel;
      }
    }

    // 우선순위 순으로 사용 가능한 모델 찾기
    const sortedModels = Object.keys(this.modelConfigs).sort((a, b) => {
      return this.modelConfigs[b].priority - this.modelConfigs[a].priority;
    });

    for (const modelKey of sortedModels) {
      if (this.isModelAvailable(modelKey)) {
        return modelKey;
      }
    }

    return null;
  }

  isModelAvailable(modelKey) {
    const config = this.modelConfigs[modelKey];
    const dailyUsed = this.dailyCounters[modelKey];
    const limiter = this.limiters[modelKey];

    return dailyUsed < config.perDay && limiter.reservoir() > 0;
  }

  async callGeminiAPI(prompt, options = {}) {
    const {
      preferredModel = null,
      temperature = 0.7,
      maxTokens = 1000,
      systemPrompt = null,
    } = options;

    const modelKey = this.getAvailableModel(preferredModel);

    if (!modelKey) {
      throw new Error(
        "No available Gemini models. All models have reached their limits."
      );
    }

    const config = this.modelConfigs[modelKey];
    const limiter = this.limiters[modelKey];

    try {
      const result = await limiter.schedule(async () => {
        this.dailyCounters[modelKey]++;

        console.log(
          `🚀 Calling ${config.fullName} (${this.dailyCounters[modelKey]}/${config.perDay})`
        );

        return await this.makeAPIRequest(config.fullName, prompt, {
          temperature,
          maxTokens,
          systemPrompt,
        });
      });

      return {
        model: config.fullName,
        modelKey: modelKey,
        response: result,
        usage: {
          daily: this.dailyCounters[modelKey],
          dailyLimit: config.perDay,
          remainingToday: config.perDay - this.dailyCounters[modelKey],
        },
      };
    } catch (error) {
      console.error(`❌ Error calling ${config.fullName}:`, error.message);

      // 에러 발생 시 다른 모델로 폴백 시도
      if (error.message.includes("quota") || error.message.includes("limit")) {
        console.log("🔄 Trying fallback model...");

        // 현재 모델을 일시적으로 비활성화
        this.dailyCounters[modelKey] = config.perDay;

        // 재귀 호출로 다른 모델 시도
        return await this.callGeminiAPI(prompt, {
          ...options,
          preferredModel: null,
        });
      }

      throw error;
    }
  }

  async makeAPIRequest(modelName, prompt, options) {
    const url = `https://generativelanguage.googleapis.com/v1beta/models/${modelName}:generateContent?key=${this.apiKey}`;

    const requestBody = {
      contents: [
        {
          parts: [
            {
              text: options.systemPrompt
                ? `${options.systemPrompt}\n\n${prompt}`
                : prompt,
            },
          ],
        },
      ],
      generationConfig: {
        temperature: options.temperature,
        maxOutputTokens: options.maxTokens,
        topP: 0.8,
        topK: 10,
      },
    };

    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        `API Error ${response.status}: ${errorData.error?.message || response.statusText}`
      );
    }

    const data = await response.json();

    if (
      !data.candidates ||
      !data.candidates[0] ||
      !data.candidates[0].content
    ) {
      throw new Error("Invalid response format from Gemini API");
    }

    return {
      text: data.candidates[0].content.parts[0].text,
      finishReason: data.candidates[0].finishReason,
      safetyRatings: data.candidates[0].safetyRatings,
      usageMetadata: data.usageMetadata,
    };
  }

  // nlpHandler.ts의 getGeminiIntentClassification 함수와 유사한 인터페이스
  async getGeminiIntentClassification(message, options = {}) {
    const systemPrompt = `당신은 회의실 예약 시스템의 의도 분류 전문가입니다. 
사용자의 메시지를 분석하여 다음 중 하나로 분류해주세요:
- ROOM_RESERVATION: 회의실 예약 요청
- ROOM_INQUIRY: 회의실 정보 문의
- ROOM_RECOMMENDATION: 회의실 추천 요청
- OTHER: 기타

JSON 형식으로 응답해주세요: {"intent": "분류결과", "confidence": 0.95, "entities": {...}}`;

    try {
      const result = await this.callGeminiAPI(message, {
        ...options,
        systemPrompt,
        temperature: 0.3, // 분류 작업이므로 낮은 temperature
        maxTokens: 500,
      });

      // JSON 파싱 시도
      try {
        const parsed = JSON.parse(result.response.text);
        return {
          ...parsed,
          model: result.model,
          usage: result.usage,
        };
      } catch (parseError) {
        console.warn("Failed to parse JSON response, returning raw text");
        return {
          intent: "OTHER",
          confidence: 0.5,
          rawResponse: result.response.text,
          model: result.model,
          usage: result.usage,
        };
      }
    } catch (error) {
      console.error("Gemini intent classification failed:", error);
      throw error;
    }
  }

  // 현재 상태 조회
  getStatus() {
    this.checkDailyReset();

    const status = {
      lastReset: this.lastResetDate,
      models: {},
    };

    Object.keys(this.modelConfigs).forEach((modelKey) => {
      const config = this.modelConfigs[modelKey];
      const limiter = this.limiters[modelKey];

      status.models[modelKey] = {
        fullName: config.fullName,
        dailyUsage: `${this.dailyCounters[modelKey]}/${config.perDay}`,
        currentReservoir: limiter.reservoir(),
        maxReservoir: config.perMinute,
        priority: config.priority,
        isAvailable: this.isModelAvailable(modelKey),
      };
    });

    return status;
  }
}

// 사용 예시
async function example() {
  const apiKey = process.env.GEMINI_API_KEY || "your-api-key-here";
  const geminiManager = new GeminiAPIManager(apiKey);

  try {
    // 기본 호출
    console.log("=== 기본 API 호출 ===");
    const result1 = await geminiManager.callGeminiAPI(
      "안녕하세요, 오늘 날씨는 어떤가요?"
    );
    console.log("Response:", result1.response.text);
    console.log("Model used:", result1.model);
    console.log("Usage:", result1.usage);

    // 의도 분류 호출
    console.log("\n=== 의도 분류 호출 ===");
    const intent = await geminiManager.getGeminiIntentClassification(
      "내일 오후 2시에 회의실 예약하고 싶어요"
    );
    console.log("Intent classification:", intent);

    // 특정 모델 지정
    console.log("\n=== 특정 모델 지정 ===");
    const result2 = await geminiManager.callGeminiAPI(
      "복잡한 분석이 필요한 질문입니다.",
      {
        preferredModel: "1.5-pro",
        temperature: 0.9,
        maxTokens: 2000,
      }
    );
    console.log("High-end model response:", result2.response.text);

    // 상태 확인
    console.log("\n=== 현재 상태 ===");
    console.table(geminiManager.getStatus().models);
  } catch (error) {
    console.error("Example failed:", error);
  }
}

module.exports = {
  GeminiAPIManager,
  example,
};

// 직접 실행 시 예시 실행
if (require.main === module) {
  example().catch(console.error);
}
