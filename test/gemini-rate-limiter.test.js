/**
 * Gemini API Rate Limiter Test with Bottleneck
 *
 * 4개의 모델별 사용량 제한:
 * 1. gemini-model-1: 분당 10회, 하루 250회
 * 2. gemini-model-2: 분당 15회, 하루 1000회
 * 3. gemini-model-3: 분당 15회, 하루 200회
 * 4. gemini-model-4: 분당 30회, 하루 200회
 */

const Bottleneck = require("bottleneck");

// 모델별 설정
const MODEL_CONFIGS = {
  "2.5-flash": {
    perMinute: 10,
    perDay: 250,
    priority: 1, // 낮은 우선순위 (제한이 가장 적음)
  },
  "2.5-flash-lite-preview-06-17": {
    perMinute: 15,
    perDay: 1000,
    priority: 4, // 높은 우선순위 (하루 제한이 가장 많음)
  },
  "2.0-flash": {
    perMinute: 15,
    perDay: 200,
    priority: 2,
  },
  "2.0-flash-lite": {
    perMinute: 30,
    perDay: 200,
    priority: 3,
  },
};

class GeminiRateLimiter {
  constructor() {
    this.limiters = {};
    this.dailyCounters = {};
    this.lastResetDate = new Date().toDateString();
    this.reservoirLevels = {}; // reservoir 레벨을 수동으로 추적

    // 각 모델별 Bottleneck 인스턴스 생성
    Object.keys(MODEL_CONFIGS).forEach((modelName) => {
      const config = MODEL_CONFIGS[modelName];

      this.limiters[modelName] = new Bottleneck({
        reservoir: config.perMinute, // 초기 토큰 수
        reservoirRefreshAmount: config.perMinute, // 리필할 토큰 수
        reservoirRefreshInterval: 60 * 1000, // 1분마다 리필
        maxConcurrent: 1, // 동시 실행 제한
        minTime: Math.floor(60000 / config.perMinute), // 최소 간격 (ms)
        id: `gemini-${modelName}`, // 디버깅용 ID
      });

      // reservoir 레벨 초기화
      this.reservoirLevels[modelName] = config.perMinute;

      // reservoir 변화 이벤트 리스너 추가
      this.limiters[modelName].on("depleted", () => {
        this.reservoirLevels[modelName] = 0;
        console.log(`🔴 ${modelName} reservoir depleted`);
      });

      this.limiters[modelName].on("message", (message) => {
        if (message === "Reservoir refilled") {
          this.reservoirLevels[modelName] = config.perMinute;
          console.log(
            `🔄 ${modelName} reservoir refilled to ${config.perMinute}`
          );
        }
      });

      // 하루 카운터 초기화
      this.dailyCounters[modelName] = 0;
    });
  }

  // 하루 카운터 리셋 체크
  checkDailyReset() {
    const today = new Date().toDateString();
    if (today !== this.lastResetDate) {
      console.log("🔄 Daily counters reset");
      Object.keys(this.dailyCounters).forEach((model) => {
        this.dailyCounters[model] = 0;
      });
      this.lastResetDate = today;
    }
  }

  // 사용 가능한 모델 찾기 (우선순위 기반)
  getAvailableModel() {
    this.checkDailyReset();

    // 우선순위 순으로 정렬된 모델 목록
    const sortedModels = Object.keys(MODEL_CONFIGS).sort((a, b) => {
      return MODEL_CONFIGS[b].priority - MODEL_CONFIGS[a].priority;
    });

    for (const modelName of sortedModels) {
      const config = MODEL_CONFIGS[modelName];
      const dailyUsed = this.dailyCounters[modelName];

      // 하루 제한 체크
      if (dailyUsed >= config.perDay) {
        console.log(
          `❌ ${modelName}: Daily limit reached (${dailyUsed}/${config.perDay})`
        );
        continue;
      }

      // Bottleneck 상태 체크 - 즉시 reservoir 예약
      const currentReservoir = this.reservoirLevels[modelName];
      if (currentReservoir > 0) {
        // 🆕 즉시 reservoir 감소 (예약)
        this.reservoirLevels[modelName]--;
        console.log(
          `✅ ${modelName}: Reserved (reservoir: ${currentReservoir - 1}, daily: ${dailyUsed}/${config.perDay})`
        );
        return modelName;
      } else {
        console.log(
          `⏳ ${modelName}: Rate limited (reservoir: ${currentReservoir})`
        );
      }
    }

    return null; // 사용 가능한 모델 없음
  }

  // API 호출 실행
  async callGeminiAPI(prompt, variantModel = null) {
    let modelName;

    if (variantModel) {
      // 특정 모델이 지정된 경우 존재 여부 확인
      if (!MODEL_CONFIGS[variantModel]) {
        throw new Error(
          `Unknown model: ${variantModel}. Available models: ${Object.keys(MODEL_CONFIGS).join(", ")}`
        );
      }
      modelName = variantModel;
    } else {
      modelName = this.getAvailableModel();
    }

    if (!modelName) {
      throw new Error("No available Gemini models at the moment");
    }

    const limiter = this.limiters[modelName];

    try {
      // Bottleneck을 통해 API 호출
      const result = await limiter.schedule(async () => {
        // 하루 카운터 증가
        this.dailyCounters[modelName]++;

        // 🆕 reservoir는 이미 getAvailableModel()에서 감소했으므로 여기서는 제거

        console.log(
          `🚀 Calling ${modelName} (daily usage: ${this.dailyCounters[modelName]}/${MODEL_CONFIGS[modelName].perDay})`
        );

        // 실제 API 호출 시뮬레이션
        return this.simulateGeminiAPICall(modelName, prompt);
      });

      return {
        model: modelName,
        result: result,
        dailyUsage: this.dailyCounters[modelName],
        dailyLimit: MODEL_CONFIGS[modelName].perDay,
      };
    } catch (error) {
      console.error(`❌ Error calling ${modelName}:`, error.message);
      throw error;
    }
  }

  // API 호출 시뮬레이션
  async simulateGeminiAPICall(modelName, prompt) {
    // 실제 구현에서는 여기에 실제 Gemini API 호출 로직이 들어감
    const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${modelName}:generateContent?key=YOUR_API_KEY`;

    // 시뮬레이션을 위한 랜덤 지연
    const delay = Math.random() * 1000 + 500; // 0.5-1.5초
    await new Promise((resolve) => setTimeout(resolve, delay));

    // 가끔 에러 시뮬레이션
    if (Math.random() < 0.1) {
      // 10% 확률로 에러
      throw new Error(`API Error from ${modelName}`);
    }

    return {
      response: `Response from ${modelName} for prompt: "${prompt.substring(0, 50)}..."`,
      timestamp: new Date().toISOString(),
      processingTime: Math.round(delay),
    };
  }

  // 현재 상태 출력
  getStatus() {
    this.checkDailyReset();

    const status = {};
    Object.keys(MODEL_CONFIGS).forEach((modelName) => {
      const config = MODEL_CONFIGS[modelName];

      status[modelName] = {
        dailyUsage: `${this.dailyCounters[modelName]}/${config.perDay}`,
        currentReservoir: this.reservoirLevels[modelName],
        maxReservoir: config.perMinute,
        priority: config.priority,
        isAvailable:
          this.dailyCounters[modelName] < config.perDay &&
          this.reservoirLevels[modelName] > 0,
      };
    });

    return status;
  }
}

// 테스트 함수들
async function testBasicUsage() {
  console.log("\n=== 기본 사용 테스트 ===");
  const rateLimiter = new GeminiRateLimiter();

  try {
    const result = await rateLimiter.callGeminiAPI("Hello, how are you?");
    console.log("✅ API Call Success:", result);
  } catch (error) {
    console.error("❌ API Call Failed:", error.message);
  }
}

async function testRateLimiting() {
  console.log("\n=== 속도 제한 테스트 ===");
  const rateLimiter = new GeminiRateLimiter();

  // 빠른 연속 호출로 속도 제한 테스트
  const promises = [];
  for (let i = 0; i < 5; i++) {
    promises.push(
      rateLimiter
        .callGeminiAPI(`Test message ${i + 1}`)
        .then((result) => console.log(`✅ Call ${i + 1}:`, result.model))
        .catch((error) => console.error(`❌ Call ${i + 1}:`, error.message))
    );
  }

  await Promise.all(promises);
}

async function testModelFallback() {
  console.log("\n=== 모델 폴백 테스트 ===");
  const rateLimiter = new GeminiRateLimiter();

  // 특정 모델의 하루 제한을 인위적으로 설정
  rateLimiter.dailyCounters["gemini-model-2"] = 999; // 거의 한계

  console.log("Status before calls:");
  console.table(rateLimiter.getStatus());

  // 여러 번 호출하여 폴백 동작 확인
  for (let i = 0; i < 3; i++) {
    try {
      const result = await rateLimiter.callGeminiAPI(`Fallback test ${i + 1}`);
      console.log(`✅ Call ${i + 1} used model:`, result.model);
    } catch (error) {
      console.error(`❌ Call ${i + 1} failed:`, error.message);
    }
  }
}

async function testSpecificModel() {
  console.log("\n=== 특정 모델 지정 테스트 ===");
  const rateLimiter = new GeminiRateLimiter();

  try {
    // 실제 존재하는 모델명 사용
    const result = await rateLimiter.callGeminiAPI(
      "Specific model test",
      "2.0-flash-lite"
    );
    console.log("✅ Specific model call:", result);
  } catch (error) {
    console.error("❌ Specific model call failed:", error.message);
  }
}

// 메인 테스트 실행
async function runAllTests() {
  console.log("🧪 Gemini Rate Limiter Tests Starting...\n");

  await testBasicUsage();
  await testRateLimiting();
  await testModelFallback();
  await testSpecificModel();

  console.log("\n🏁 All tests completed!");
}

// 모듈 export (Node.js 환경에서 사용 시)
if (typeof module !== "undefined" && module.exports) {
  module.exports = {
    GeminiRateLimiter,
    MODEL_CONFIGS,
    runAllTests,
  };
}

// 직접 실행 시 테스트 실행
if (require.main === module) {
  runAllTests().catch(console.error);
}
