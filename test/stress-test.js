/**
 * Gemini API Rate Limiter Stress Test
 * 대량의 요청으로 rate limiting과 model fallback 테스트
 */

const { GeminiRateLimiter } = require('./gemini-rate-limiter.test.js');

class StressTester {
  constructor() {
    this.rateLimiter = new GeminiRateLimiter();
    this.results = {
      total: 0,
      successful: 0,
      failed: 0,
      modelUsage: {},
      errors: [],
      startTime: null,
      endTime: null
    };
  }

  async runStressTest(totalRequests = 50, concurrency = 10) {
    console.log(`🚀 Starting stress test: ${totalRequests} requests with ${concurrency} concurrent`);
    console.log('Initial status:');
    console.table(this.rateLimiter.getStatus());

    this.results.startTime = Date.now();
    this.results.total = totalRequests;

    // 요청을 배치로 나누어 실행
    const batches = [];
    for (let i = 0; i < totalRequests; i += concurrency) {
      const batchSize = Math.min(concurrency, totalRequests - i);
      batches.push(this.createBatch(i, batchSize));
    }

    // 각 배치를 순차적으로 실행
    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      console.log(`\n📦 Executing batch ${batchIndex + 1}/${batches.length}`);
      
      const batchPromises = batches[batchIndex];
      const batchResults = await Promise.allSettled(batchPromises);
      
      this.processBatchResults(batchResults, batchIndex);
      
      // 배치 간 잠시 대기 (선택적)
      if (batchIndex < batches.length - 1) {
        await this.sleep(1000); // 1초 대기
      }
    }

    this.results.endTime = Date.now();
    this.printResults();
  }

  createBatch(startIndex, batchSize) {
    const promises = [];
    
    for (let i = 0; i < batchSize; i++) {
      const requestIndex = startIndex + i;
      const promise = this.makeTestRequest(requestIndex);
      promises.push(promise);
    }
    
    return promises;
  }

  async makeTestRequest(requestIndex) {
    const prompts = [
      '안녕하세요, 회의실 예약하고 싶어요',
      '내일 오후 2시에 미팅룸 필요해요',
      '4명이 사용할 수 있는 회의실 추천해주세요',
      '현재 예약 가능한 회의실이 있나요?',
      '회의실 예약을 취소하고 싶어요',
      '다음 주 월요일 회의실 상황 알려주세요'
    ];
    
    const prompt = prompts[requestIndex % prompts.length] + ` (Request #${requestIndex + 1})`;
    
    try {
      const startTime = Date.now();
      const result = await this.rateLimiter.callGeminiAPI(prompt);
      const endTime = Date.now();
      
      return {
        success: true,
        requestIndex: requestIndex + 1,
        model: result.model,
        responseTime: endTime - startTime,
        dailyUsage: result.dailyUsage,
        timestamp: new Date().toISOString()
      };
      
    } catch (error) {
      return {
        success: false,
        requestIndex: requestIndex + 1,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  processBatchResults(batchResults, batchIndex) {
    batchResults.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        const data = result.value;
        
        if (data.success) {
          this.results.successful++;
          
          // 모델 사용량 추적
          if (!this.results.modelUsage[data.model]) {
            this.results.modelUsage[data.model] = {
              count: 0,
              totalResponseTime: 0,
              avgResponseTime: 0
            };
          }
          
          this.results.modelUsage[data.model].count++;
          this.results.modelUsage[data.model].totalResponseTime += data.responseTime;
          this.results.modelUsage[data.model].avgResponseTime = 
            this.results.modelUsage[data.model].totalResponseTime / 
            this.results.modelUsage[data.model].count;
          
          console.log(`✅ Request ${data.requestIndex}: ${data.model} (${data.responseTime}ms)`);
        } else {
          this.results.failed++;
          this.results.errors.push({
            request: data.requestIndex,
            error: data.error,
            timestamp: data.timestamp
          });
          console.log(`❌ Request ${data.requestIndex}: ${data.error}`);
        }
      } else {
        this.results.failed++;
        this.results.errors.push({
          request: `batch-${batchIndex}-${index}`,
          error: result.reason?.message || 'Unknown error',
          timestamp: new Date().toISOString()
        });
        console.log(`❌ Batch ${batchIndex} Request ${index}: ${result.reason?.message}`);
      }
    });

    // 배치 완료 후 현재 상태 출력
    console.log(`\n📊 Batch ${batchIndex + 1} completed. Current status:`);
    console.table(this.rateLimiter.getStatus());
  }

  printResults() {
    const duration = this.results.endTime - this.results.startTime;
    const successRate = (this.results.successful / this.results.total * 100).toFixed(2);
    const requestsPerSecond = (this.results.total / (duration / 1000)).toFixed(2);

    console.log('\n' + '='.repeat(60));
    console.log('🏁 STRESS TEST RESULTS');
    console.log('='.repeat(60));
    
    console.log(`📊 Summary:`);
    console.log(`   Total Requests: ${this.results.total}`);
    console.log(`   Successful: ${this.results.successful}`);
    console.log(`   Failed: ${this.results.failed}`);
    console.log(`   Success Rate: ${successRate}%`);
    console.log(`   Duration: ${(duration / 1000).toFixed(2)}s`);
    console.log(`   Requests/sec: ${requestsPerSecond}`);

    console.log(`\n🤖 Model Usage:`);
    Object.keys(this.results.modelUsage).forEach(model => {
      const usage = this.results.modelUsage[model];
      console.log(`   ${model}:`);
      console.log(`     Requests: ${usage.count}`);
      console.log(`     Avg Response Time: ${usage.avgResponseTime.toFixed(0)}ms`);
      console.log(`     Usage %: ${(usage.count / this.results.successful * 100).toFixed(1)}%`);
    });

    if (this.results.errors.length > 0) {
      console.log(`\n❌ Errors (${this.results.errors.length}):`);
      this.results.errors.slice(0, 10).forEach(error => {
        console.log(`   Request ${error.request}: ${error.error}`);
      });
      
      if (this.results.errors.length > 10) {
        console.log(`   ... and ${this.results.errors.length - 10} more errors`);
      }
    }

    console.log(`\n🔍 Final Status:`);
    console.table(this.rateLimiter.getStatus());
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 다양한 테스트 시나리오
async function runLightTest() {
  console.log('🟢 Running Light Test (10 requests, 3 concurrent)');
  const tester = new StressTester();
  await tester.runStressTest(10, 3);
}

async function runMediumTest() {
  console.log('\n🟡 Running Medium Test (30 requests, 5 concurrent)');
  const tester = new StressTester();
  await tester.runStressTest(30, 5);
}

async function runHeavyTest() {
  console.log('\n🔴 Running Heavy Test (100 requests, 10 concurrent)');
  const tester = new StressTester();
  await tester.runStressTest(100, 10);
}

async function runCustomTest() {
  const args = process.argv.slice(2);
  const totalRequests = parseInt(args[0]) || 20;
  const concurrency = parseInt(args[1]) || 5;
  
  console.log(`\n⚙️ Running Custom Test (${totalRequests} requests, ${concurrency} concurrent)`);
  const tester = new StressTester();
  await tester.runStressTest(totalRequests, concurrency);
}

// 메인 실행 함수
async function main() {
  const testType = process.argv[2] || 'light';
  
  console.log('🧪 Gemini API Rate Limiter Stress Test\n');
  
  switch (testType.toLowerCase()) {
    case 'light':
      await runLightTest();
      break;
    case 'medium':
      await runMediumTest();
      break;
    case 'heavy':
      await runHeavyTest();
      break;
    case 'all':
      await runLightTest();
      await runMediumTest();
      await runHeavyTest();
      break;
    case 'custom':
      await runCustomTest();
      break;
    default:
      console.log('Usage: node stress-test.js [light|medium|heavy|all|custom] [requests] [concurrency]');
      console.log('Examples:');
      console.log('  node stress-test.js light');
      console.log('  node stress-test.js custom 50 8');
      break;
  }
}

// 직접 실행 시 메인 함수 실행
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  StressTester,
  runLightTest,
  runMediumTest,
  runHeavyTest
};
