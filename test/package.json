{"name": "gemini-rate-limiter-test", "version": "1.0.0", "description": "Gemini API Rate Limiter Test with Bottleneck", "main": "gemini-rate-limiter.test.js", "scripts": {"test": "node gemini-rate-limiter.test.js", "test:integration": "node gemini-integration-example.js", "test:stress": "node stress-test.js", "install-deps": "npm install bottleneck node-fetch"}, "dependencies": {"bottleneck": "^2.19.5", "node-fetch": "^2.7.0"}, "devDependencies": {"jest": "^29.0.0"}, "keywords": ["gemini", "api", "rate-limiting", "bottleneck", "load-balancing"], "author": "Your Name", "license": "MIT"}