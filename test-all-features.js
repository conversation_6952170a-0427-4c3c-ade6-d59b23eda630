#!/usr/bin/env node

// [통합테스트] 전체 프로젝트 기능 터미널 테스트 스크립트

const readline = require("readline");
const path = require("path");

// TypeScript 파일을 직접 실행하기 위한 설정
require("ts-node/register");

// 환경 변수 로드
require("dotenv").config();

// 필요한 함수들 import
const { parseMessage, getMyMeetings } = require("./src/nlpHandler.ts");
const { getRoomEvents } = require("./src/google-calendar.ts");
const { ROOM_CALENDARS } = require("./src/meetingRooms.ts");
const { getRoomReservationBlockKit } = require("./src/formatRoomEvents.ts");

// 터미널 입출력 인터페이스 설정
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

// 테스트용 사용자 정보
const TEST_USER_EMAIL = process.env.TEST_USER_EMAIL || "<EMAIL>";
const TEST_USER_ID = "U1234567890"; // 테스트용 Slack 사용자 ID

console.log("🤖 회의실 예약 봇 통합 테스트 시작!");
console.log("=" * 60);
console.log(`📧 테스트 사용자: ${TEST_USER_EMAIL}`);
console.log("💡 모든 기능을 테스트할 수 있습니다!");
console.log("");
console.log("🎯 지원 기능:");
console.log('  1. 📅 내 회의 조회 - "오늘 나 회의 있어?"');
console.log('  2. 📊 회의실 현황 - "내일 회의실 현황 알려줘"');
console.log('  3. 💡 회의실 추천 - "회의실 추천해줘"');
console.log('  4. 🏢 회의실 정보 - "회의실 정보 알려줘"');
console.log('  5. 📝 예약 요청 - "내일 2시 The1 예약해줘"');
console.log('  6. 💬 인사/잡담 - "안녕", "도움말"');
console.log("");
console.log('❌ 종료하려면 "exit" 또는 "quit" 입력');
console.log("=" * 60);
console.log("");

// 색상 출력을 위한 함수들
const colors = {
  reset: "\x1b[0m",
  bright: "\x1b[1m",
  red: "\x1b[31m",
  green: "\x1b[32m",
  yellow: "\x1b[33m",
  blue: "\x1b[34m",
  magenta: "\x1b[35m",
  cyan: "\x1b[36m",
};

function colorLog(text, color = colors.reset) {
  console.log(`${color}${text}${colors.reset}`);
}

// 의도 분류 결과를 분석하는 함수
function analyzeIntent(parsedData) {
  const intents = [];

  if (parsedData.isGreetingOrChitchat) intents.push("💬 인사/잡담");
  if (parsedData.isMyMeetingRequest) intents.push("📅 내 회의 조회");
  if (parsedData.isAvailabilityCheck) intents.push("📊 회의실 현황");
  if (parsedData.isRecommendationRequest) intents.push("💡 회의실 추천");
  if (parsedData.isReservationRequest) intents.push("📝 예약 요청");
  if (parsedData.isRoomInfoRequest) intents.push("🏢 회의실 정보");
  if (parsedData.needsSubjectConfirmation) intents.push("🤔 주체 확인 필요");

  return intents.length > 0 ? intents : ["❓ 분류되지 않음"];
}

// 파싱 결과를 예쁘게 출력하는 함수
function displayParsingResult(parsedData) {
  colorLog("\n📊 파싱 결과:", colors.cyan);
  console.log("─".repeat(40));

  const intents = analyzeIntent(parsedData);
  colorLog(`🎯 감지된 의도: ${intents.join(", ")}`, colors.bright);

  if (parsedData.confidence !== undefined) {
    const confidenceColor =
      parsedData.confidence > 0.7
        ? colors.green
        : parsedData.confidence > 0.4
          ? colors.yellow
          : colors.red;
    colorLog(
      `🎲 신뢰도: ${(parsedData.confidence * 100).toFixed(1)}%`,
      confidenceColor
    );
  }

  if (parsedData.dateRange) {
    colorLog(
      `📅 기간: ${parsedData.dateRange.start} ~ ${parsedData.dateRange.end}`,
      colors.blue
    );
  } else if (parsedData.date) {
    colorLog(`📅 날짜: ${parsedData.date}`, colors.blue);
  }

  if (parsedData.time) {
    colorLog(`⏰ 시간: ${parsedData.time}`, colors.blue);
  }

  if (parsedData.room) {
    colorLog(`🏢 회의실: ${parsedData.room}`, colors.blue);
  }

  if (parsedData.attendeeCount) {
    colorLog(`👥 참석 인원: ${parsedData.attendeeCount}명`, colors.blue);
  }

  if (parsedData.duration) {
    colorLog(`⏱️ 지속시간: ${parsedData.duration}분`, colors.blue);
  }

  if (parsedData.needsRoomSelection) {
    colorLog(
      `🔄 회의실 선택 필요: ${parsedData.needsRoomSelection}`,
      colors.yellow
    );
    if (parsedData.possibleRooms) {
      colorLog(
        `   가능한 회의실: ${parsedData.possibleRooms.join(", ")}`,
        colors.yellow
      );
    }
  }

  if (parsedData.geminiReply) {
    colorLog(`🤖 AI 응답: ${parsedData.geminiReply}`, colors.magenta);
  }
}

// 기능별 시뮬레이션 함수
function simulateFeatureResponse(parsedData) {
  colorLog("\n🎭 기능 시뮬레이션:", colors.green);
  console.log("─".repeat(40));

  if (parsedData.isGreetingOrChitchat) {
    if (parsedData.geminiReply) {
      colorLog(`💬 봇 응답: ${parsedData.geminiReply}`, colors.magenta);
    } else {
      colorLog(
        "💬 봇 응답: 안녕하세요! 회의실 예약을 도와드리겠습니다.",
        colors.magenta
      );
    }
  } else if (parsedData.isMyMeetingRequest) {
    colorLog("📅 내 회의 조회 기능 실행됨", colors.green);
    colorLog("   → getMyMeetings() 함수 호출", colors.cyan);
    colorLog("   → Google Calendar에서 내 참석 회의 검색", colors.cyan);
    colorLog("   → 날짜별/요일별 형식으로 출력", colors.cyan);
  } else if (parsedData.isAvailabilityCheck) {
    colorLog("📊 회의실 현황 조회 기능 실행됨", colors.green);
    colorLog("   → getRoomEvents() 함수 호출", colors.cyan);
    colorLog("   → 지정된 날짜의 모든 회의실 현황 조회", colors.cyan);
    colorLog("   → Block Kit 형태로 현황 출력", colors.cyan);
  } else if (parsedData.isRecommendationRequest) {
    colorLog("💡 회의실 추천 기능 실행됨", colors.green);
    colorLog("   → recommendAvailableRooms() 함수 호출", colors.cyan);
    colorLog("   → 빈 회의실 및 시간대 추천", colors.cyan);
    colorLog("   → 참석 인원수 고려한 적합한 회의실 제안", colors.cyan);
  } else if (parsedData.isReservationRequest) {
    colorLog("📝 예약 요청 기능 실행됨", colors.green);
    colorLog("   → 예약 가능성 확인", colors.cyan);
    colorLog("   → Google Calendar 예약 URL 생성", colors.cyan);
    colorLog("   → 예약 확인 버튼 제공", colors.cyan);
  } else if (parsedData.isRoomInfoRequest) {
    colorLog("🏢 회의실 정보 기능 실행됨", colors.green);
    colorLog("   → buildRoomInfoBlocks() 함수 호출", colors.cyan);
    colorLog("   → 모든 회의실 정보 및 수용 인원 출력", colors.cyan);
  } else if (parsedData.needsSubjectConfirmation) {
    colorLog("🤔 주체 확인 기능 실행됨", colors.yellow);
    colorLog('   → "내 예약 확인" vs "전체 현황 확인" 버튼 제공', colors.cyan);
    colorLog("   → 사용자 선택에 따라 해당 기능 실행", colors.cyan);
  } else {
    colorLog("❓ 분류되지 않은 요청", colors.red);
    colorLog("   → 기본 도움말 메시지 출력", colors.cyan);
  }
}

// 실제 기능 실행 함수
async function executeFeature(parsedData) {
  colorLog("\n🚀 실제 기능 실행:", colors.green);
  console.log("─".repeat(40));

  try {
    if (parsedData.isGreetingOrChitchat) {
      if (parsedData.geminiReply) {
        colorLog(`💬 봇 응답: ${parsedData.geminiReply}`, colors.magenta);
      } else {
        colorLog(
          "💬 봇 응답: 안녕하세요! 회의실 예약을 도와드리겠습니다.",
          colors.magenta
        );
      }
    } else if (parsedData.isMyMeetingRequest) {
      colorLog("📅 내 회의 조회 실행 중...", colors.green);

      // [내회의관련-수정] 날짜 설정 (다중 날짜 지원)
      let dateList = [];
      if (parsedData.dateRange) {
        // 다음주 전체 등 기간이 지정된 경우
        const startDate = new Date(parsedData.dateRange.start);
        const endDate = new Date(parsedData.dateRange.end);

        colorLog(
          `📅 기간 조회: ${parsedData.dateRange.start} ~ ${parsedData.dateRange.end}`,
          colors.blue
        );

        // 시작일부터 종료일까지 모든 날짜 생성
        for (
          let d = new Date(startDate);
          d <= endDate;
          d.setDate(d.getDate() + 1)
        ) {
          dateList.push(d.toISOString().split("T")[0]);
        }
      } else if (parsedData.date) {
        dateList = [parsedData.date];
      } else {
        const today = new Date();
        dateList = [today.toISOString().split("T")[0]];
      }

      const result = await getMyMeetings(TEST_USER_EMAIL, dateList);

      if (result.isSuccess) {
        colorLog(`✅ ${result.message}`, colors.green);

        if (result.blocks && result.blocks.length > 0) {
          colorLog("\n📱 실제 Slack 출력:", colors.cyan);
          console.log("─".repeat(30));

          result.blocks.forEach((block) => {
            if (block.type === "section" && block.text && block.text.text) {
              let text = block.text.text
                .replace(/\*([^*]+)\*/g, "$1")
                .replace(/`([^`]+)`/g, "$1")
                .replace(/:([^:]+):/g, "[$1]");

              console.log(text);
            }
          });
        }
      } else {
        colorLog(`❌ ${result.message}`, colors.red);
      }
    } else if (parsedData.isAvailabilityCheck) {
      colorLog("📊 회의실 현황 조회 실행 중...", colors.green);

      // 날짜 설정
      const dateStr = parsedData.date || new Date().toISOString().split("T")[0];

      // [내회의관련-수정] 특정 회의실이 지정된 경우 해당 회의실만 조회
      const roomData = {};
      let roomNames;

      if (parsedData.room) {
        roomNames = [parsedData.room];
        colorLog(`🏢 특정 회의실 조회: ${parsedData.room}`, colors.blue);
      } else {
        roomNames = Object.keys(ROOM_CALENDARS);
        colorLog("🏢 전체 회의실 조회", colors.blue);
      }

      for (const roomName of roomNames) {
        try {
          const calendarId = ROOM_CALENDARS[roomName];
          if (calendarId) {
            const events = await getRoomEvents(calendarId, dateStr);
            roomData[roomName] = { events };
          } else {
            console.error(`❌ ${roomName}의 캘린더 ID를 찾을 수 없습니다.`);
            roomData[roomName] = { events: [] };
          }
        } catch (error) {
          console.error(`❌ ${roomName} 조회 실패:`, error.message);
          roomData[roomName] = { events: [] };
        }
      }

      // Block Kit 형태로 변환
      const blocks = await getRoomReservationBlockKit(dateStr, roomData);

      if (blocks && blocks.length > 0) {
        colorLog("\n📱 실제 Slack 출력:", colors.cyan);
        console.log("─".repeat(30));

        blocks.forEach((block) => {
          if (block.type === "section" && block.text && block.text.text) {
            let text = block.text.text
              .replace(/\*([^*]+)\*/g, "$1")
              .replace(/`([^`]+)`/g, "$1")
              .replace(/:([^:]+):/g, "[$1]");

            console.log(text);
          } else if (block.type === "context" && block.elements) {
            block.elements.forEach((element) => {
              if (element.text) {
                let text = element.text
                  .replace(/\*([^*]+)\*/g, "$1")
                  .replace(/`([^`]+)`/g, "$1")
                  .replace(/:([^:]+):/g, "[$1]");

                console.log(text);
              }
            });
          }
        });
      } else {
        colorLog("📊 회의실 현황 데이터가 없습니다.", colors.yellow);
      }
    } else if (parsedData.isRecommendationRequest) {
      colorLog("💡 회의실 추천 기능 실행됨", colors.green);
      colorLog("   → recommendAvailableRooms() 함수 호출", colors.cyan);
      colorLog("   → 빈 회의실 및 시간대 추천", colors.cyan);
      colorLog("   → 참석 인원수 고려한 적합한 회의실 제안", colors.cyan);
    } else if (parsedData.isReservationRequest) {
      colorLog("📝 예약 요청 기능 실행됨", colors.green);
      colorLog("   → 예약 가능성 확인", colors.cyan);
      colorLog("   → Google Calendar 예약 URL 생성", colors.cyan);
      colorLog("   → 예약 확인 버튼 제공", colors.cyan);
    } else if (parsedData.isRoomInfoRequest) {
      colorLog("🏢 회의실 정보 기능 실행됨", colors.green);
      colorLog("   → buildRoomInfoBlocks() 함수 호출", colors.cyan);
      colorLog("   → 모든 회의실 정보 및 수용 인원 출력", colors.cyan);
    } else if (parsedData.needsSubjectConfirmation) {
      colorLog("🤔 주체 확인 기능 실행됨", colors.yellow);
      colorLog(
        '   → "내 예약 확인" vs "전체 현황 확인" 버튼 제공',
        colors.cyan
      );
      colorLog("   → 사용자 선택에 따라 해당 기능 실행", colors.cyan);
    } else {
      colorLog("💡 다른 기능들은 시뮬레이션으로 표시됩니다.", colors.yellow);
      simulateFeatureResponse(parsedData);
    }
  } catch (error) {
    colorLog(`💥 기능 실행 오류: ${error.message}`, colors.red);
  }
}

// 메시지 처리 함수
async function processMessage(userInput) {
  try {
    colorLog(`\n🔍 입력: "${userInput}"`, colors.blue);
    colorLog("⏳ 파싱 중...", colors.yellow);

    // 자연어 파싱 실행
    const parsedData = await parseMessage(userInput);

    // 파싱 결과 출력
    displayParsingResult(parsedData);

    // 실제 기능 실행
    await executeFeature(parsedData);

    // 추가 정보 출력
    if (parsedData.usedAI !== undefined) {
      const aiUsed = parsedData.usedAI ? "🤖 AI 파싱" : "📏 규칙 기반 파싱";
      colorLog(`\n🔧 파싱 방식: ${aiUsed}`, colors.cyan);
    }
  } catch (error) {
    colorLog("\n💥 오류 발생:", colors.red);
    console.error(error.message);
    if (error.stack) {
      console.error("스택 트레이스:", error.stack);
    }
  }
}

// 메인 대화 루프
function startChat() {
  rl.question("💬 메시지를 입력하세요: ", async (input) => {
    const trimmedInput = input.trim();

    // 종료 명령어 확인
    if (
      trimmedInput.toLowerCase() === "exit" ||
      trimmedInput.toLowerCase() === "quit"
    ) {
      colorLog("\n👋 테스트를 종료합니다. 안녕히 가세요!", colors.green);
      rl.close();
      return;
    }

    // 빈 입력 처리
    if (!trimmedInput) {
      colorLog("❓ 메시지를 입력해주세요.", colors.yellow);
      startChat();
      return;
    }

    // 메시지 처리
    await processMessage(trimmedInput);

    colorLog("\n" + "─".repeat(60), colors.cyan);

    // 다음 입력 대기
    startChat();
  });
}

// 프로그램 시작
startChat();

// 프로세스 종료 시 정리
process.on("SIGINT", () => {
  colorLog("\n\n👋 프로그램을 종료합니다.", colors.green);
  rl.close();
  process.exit(0);
});
