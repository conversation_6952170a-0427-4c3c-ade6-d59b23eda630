// 지속시간 파싱 테스트 스크립트
const testMessages = [
  "오늘 라이트룸 1시부터 2시간 예약해줘",
  "내일 더원 오후 2시 3시간 예약",
  "다음주 월요일 포커스룸 10시 90분 예약해줘",
  "오늘 다크룸 3시부터 1시간 30분 예약",
  "라이트룸 2시간30분 예약 가능해?",
  "더투 4시 예약해줘", // 지속시간 없음
];

// 지속시간 파싱 함수 (nlpHandler.ts에서 가져온 로직)
function parseDuration(message) {
  const durationPatterns = [
    /(\d+)시간(\d+)분/, // "2시간30분" - 먼저 체크
    /(\d+)시간\s*(\d+)분/, // "2시간 30분" (공백 포함)
    /(\d+)시간/, // "2시간"
    /(\d+)분/, // "90분"
  ];

  for (const pattern of durationPatterns) {
    const match = message.match(pattern);
    if (match) {
      let durationMinutes = 0;
      
      if (pattern.source.includes('시간') && pattern.source.includes('분')) {
        // "2시간30분" 또는 "2시간 30분" 형태
        const hours = parseInt(match[1]);
        const minutes = parseInt(match[2]);
        durationMinutes = hours * 60 + minutes;
      } else if (pattern.source.includes('시간')) {
        // "2시간" 형태
        const hours = parseInt(match[1]);
        durationMinutes = hours * 60;
      } else if (pattern.source.includes('분')) {
        // "90분" 형태
        durationMinutes = parseInt(match[1]);
      }
      
      if (durationMinutes > 0 && durationMinutes <= 480) { // 최대 8시간
        return durationMinutes;
      }
    }
  }
  return null;
}

console.log("=== 지속시간 파싱 테스트 ===\n");

testMessages.forEach((message, index) => {
  const duration = parseDuration(message);
  console.log(`${index + 1}. "${message}"`);
  if (duration) {
    const hours = Math.floor(duration / 60);
    const minutes = duration % 60;
    const durationText = hours > 0 
      ? (minutes > 0 ? `${hours}시간 ${minutes}분` : `${hours}시간`)
      : `${minutes}분`;
    console.log(`   ✅ 감지된 지속시간: ${duration}분 (${durationText})`);
  } else {
    console.log(`   ❌ 지속시간 감지되지 않음 (기본값 사용)`);
  }
  console.log("");
});
