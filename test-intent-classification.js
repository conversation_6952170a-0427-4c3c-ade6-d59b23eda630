// 의도 분류 테스트
const { parseMessage } = require('./dist/nlpHandler');

async function testIntentClassification() {
  const testMessages = [
    // 내 회의 조회 요청
    "내 회의 있어?",
    "오늘 나 회의 뭐있어?",
    "내 예약 알려줘",
    "내가 참석하는 회의 보여줘",
    
    // 주체 확인 필요한 요청
    "오늘 회의 있어?",
    "내일 예약 알려줘",
    "예약 확인해줘",
    
    // 전체 현황 조회
    "오늘 회의실 현황",
    "내일 The1 예약 상황",
    "회의실 상태 알려줘",
    
    // 회의실 추천
    "빈 회의실 알려줘",
    "회의실 추천해줘",
    "가능한 회의실 있어?",
    
    // 직접 예약
    "내일 2시 The1 예약해줘",
    "오늘 라이트룸 1시부터 30분 예약해줘",
    "포커스룸 예약하고 싶어"
  ];

  console.log("=== Gemini 의도 분류 테스트 ===\n");

  for (let i = 0; i < testMessages.length; i++) {
    const message = testMessages[i];
    console.log(`${i + 1}. "${message}"`);
    
    try {
      const result = await parseMessage(message);
      
      console.log("   파싱 결과:");
      if (result.isMyMeetingRequest) {
        console.log("   ✅ 내 회의 조회 요청으로 분류됨");
      } else if (result.needsSubjectConfirmation) {
        console.log("   ❓ 주체 확인 필요한 요청으로 분류됨");
      } else if (result.isAvailabilityCheck) {
        console.log("   📊 전체 현황 조회로 분류됨");
      } else if (result.isReservationRequest) {
        console.log("   📝 직접 예약 요청으로 분류됨");
      } else {
        console.log("   ❌ 분류되지 않음");
      }
      
      if (result.confidence) {
        console.log(`   신뢰도: ${result.confidence}`);
      }
      if (result.reasoning) {
        console.log(`   근거: ${result.reasoning}`);
      }
      
    } catch (error) {
      console.log("   ❌ 파싱 에러:", error.message);
    }
    
    console.log("");
  }
}

testIntentClassification();
