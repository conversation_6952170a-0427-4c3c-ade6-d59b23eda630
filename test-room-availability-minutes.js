// 분 단위 회의실 가용성 테스트
const { isRoomAvailable } = require('./dist/roomAvailability');

console.log("=== 분 단위 회의실 가용성 테스트 ===\n");

// 테스트 시나리오: 2시 30분부터 3시까지 예약이 있는 상황
const mockEvents = [
  {
    start: "14:30", // 2시 30분부터
    end: "15:00",   // 3시까지
    summary: "기존 예약"
  }
];

console.log("기존 예약:", mockEvents);
console.log("");

// 테스트 케이스들
const testCases = [
  {
    description: "2시 00분 체크 (예약 가능해야 함)",
    hour: 14,
    minute: 0,
    expected: true
  },
  {
    description: "2시 15분 체크 (예약 가능해야 함)",
    hour: 14,
    minute: 15,
    expected: true
  },
  {
    description: "2시 30분 체크 (예약 불가능해야 함)",
    hour: 14,
    minute: 30,
    expected: false
  },
  {
    description: "2시 45분 체크 (예약 불가능해야 함)",
    hour: 14,
    minute: 45,
    expected: false
  },
  {
    description: "3시 00분 체크 (예약 가능해야 함)",
    hour: 15,
    minute: 0,
    expected: true
  }
];

testCases.forEach((testCase, index) => {
  console.log(`${index + 1}. ${testCase.description}`);
  
  const isAvailable = isRoomAvailable(
    mockEvents,
    testCase.hour,
    "Focus Room (East)",
    testCase.minute
  );
  
  const timeStr = `${testCase.hour}:${testCase.minute.toString().padStart(2, '0')}`;
  
  if (isAvailable === testCase.expected) {
    console.log(`   ✅ 정상: ${timeStr} - ${isAvailable ? '예약 가능' : '예약 불가능'}`);
  } else {
    console.log(`   ❌ 오류: ${timeStr} - 예상(${testCase.expected ? '가능' : '불가능'}), 실제(${isAvailable ? '가능' : '불가능'})`);
  }
  
  console.log("");
});
