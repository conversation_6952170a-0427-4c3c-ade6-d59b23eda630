// 지속시간 충돌 테스트 시나리오
const { getMaxAvailableTime } = require('./dist/roomAvailability');

// 테스트 이벤트 데이터 (1시~2시, 3시~4시에 예약이 있는 상황)
const testEvents = [
  { start: "13:00", end: "14:00", summary: "기존 회의 1" },
  { start: "15:00", end: "16:00", summary: "기존 회의 2" }
];

console.log("=== 지속시간 충돌 테스트 ===\n");

// 테스트 시나리오들
const testCases = [
  {
    description: "12시부터 2시간 요청 (13시에 예약 있음)",
    startHour: 12,
    startMinute: 0,
    requestedDuration: 120, // 2시간
    expectedMaxMinutes: 60 // 12시~13시만 가능
  },
  {
    description: "14시부터 2시간 요청 (15시에 예약 있음)", 
    startHour: 14,
    startMinute: 0,
    requestedDuration: 120, // 2시간
    expectedMaxMinutes: 60 // 14시~15시만 가능
  },
  {
    description: "14시 30분부터 1시간 요청 (15시에 예약 있음)",
    startHour: 14,
    startMinute: 30,
    requestedDuration: 60, // 1시간
    expectedMaxMinutes: 30 // 14:30~15:00만 가능
  },
  {
    description: "16시부터 3시간 요청 (예약 없음)",
    startHour: 16,
    startMinute: 0,
    requestedDuration: 180, // 3시간
    expectedMaxMinutes: 360 // 16시~22시까지 가능 (6시간)
  }
];

testCases.forEach((testCase, index) => {
  console.log(`${index + 1}. ${testCase.description}`);
  
  const maxEndTime = getMaxAvailableTime(
    testEvents, 
    testCase.startHour, 
    testCase.startMinute
  );
  
  const maxAvailableMinutes = 
    (maxEndTime.hour * 60 + maxEndTime.minute) - 
    (testCase.startHour * 60 + testCase.startMinute);
  
  const startTimeStr = `${testCase.startHour}:${testCase.startMinute.toString().padStart(2, '0')}`;
  const maxEndTimeStr = `${maxEndTime.hour}:${maxEndTime.minute.toString().padStart(2, '0')}`;
  
  console.log(`   시작 시간: ${startTimeStr}`);
  console.log(`   최대 종료 시간: ${maxEndTimeStr}`);
  console.log(`   최대 예약 가능: ${maxAvailableMinutes}분`);
  console.log(`   요청 지속시간: ${testCase.requestedDuration}분`);
  
  if (testCase.requestedDuration > maxAvailableMinutes) {
    const reqHours = Math.floor(testCase.requestedDuration / 60);
    const reqMinutes = testCase.requestedDuration % 60;
    const maxHours = Math.floor(maxAvailableMinutes / 60);
    const maxMins = maxAvailableMinutes % 60;
    
    const reqText = reqHours > 0 
      ? (reqMinutes > 0 ? `${reqHours}시간 ${reqMinutes}분` : `${reqHours}시간`)
      : `${reqMinutes}분`;
    const maxText = maxHours > 0 
      ? (maxMins > 0 ? `${maxHours}시간 ${maxMins}분` : `${maxHours}시간`)
      : `${maxMins}분`;
    
    console.log(`   ❌ 예약 불가: 요청(${reqText}) > 가능(${maxText})`);
    console.log(`   💡 제안: ${maxText}만 예약 가능`);
  } else {
    console.log(`   ✅ 예약 가능`);
  }
  
  console.log("");
});
