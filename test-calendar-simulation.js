// Google Calendar 이벤트 시뮬레이션 테스트
const { getMaxAvailableTime } = require('./dist/roomAvailability');

// 2시부터 예약이 있는 상황 시뮬레이션
const mockEvents = [
  {
    start: "14:00", // 2시부터
    end: "15:00",   // 3시까지
    summary: "기존 예약"
  }
];

console.log("=== Google Calendar 이벤트 시뮬레이션 ===");
console.log("기존 예약:", mockEvents);
console.log("");

// 1시부터 3시간 예약 요청 테스트
const requestHour = 13; // 1시
const requestMinute = 0;
const requestedDuration = 180; // 3시간

console.log(`요청: ${requestHour}:${requestMinute.toString().padStart(2, '0')}부터 ${requestedDuration}분(3시간) 예약`);

const maxEndTime = getMaxAvailableTime(mockEvents, requestHour, requestMinute);
const maxAvailableMinutes = 
  (maxEndTime.hour * 60 + maxEndTime.minute) - (requestHour * 60 + requestMinute);

console.log("");
console.log("계산 결과:");
console.log(`- 최대 종료 시간: ${maxEndTime.hour}:${maxEndTime.minute.toString().padStart(2, '0')}`);
console.log(`- 최대 예약 가능 시간: ${maxAvailableMinutes}분`);
console.log(`- 요청 지속시간: ${requestedDuration}분`);
console.log("");

if (requestedDuration > maxAvailableMinutes) {
  const reqHours = Math.floor(requestedDuration / 60);
  const reqMinutes = requestedDuration % 60;
  const maxHours = Math.floor(maxAvailableMinutes / 60);
  const maxMins = maxAvailableMinutes % 60;
  
  const reqText = reqHours > 0 
    ? (reqMinutes > 0 ? `${reqHours}시간 ${reqMinutes}분` : `${reqHours}시간`)
    : `${reqMinutes}분`;
  const maxText = maxHours > 0 
    ? (maxMins > 0 ? `${maxHours}시간 ${maxMins}분` : `${maxHours}시간`)
    : `${maxMins}분`;
  
  console.log(`❌ 예약 불가: 요청(${reqText}) > 가능(${maxText})`);
  console.log(`💡 에러 메시지가 표시되어야 함`);
} else {
  console.log(`✅ 예약 가능`);
  console.log(`⚠️ 예약 링크가 생성되어서는 안됨`);
}

console.log("");
console.log("=== 예상 동작 ===");
console.log("1. 바로 예약 확정 분기 진입");
console.log("2. maxAvailableMinutes = 60분 계산");
console.log("3. requestedDuration(180분) > maxAvailableMinutes(60분) 감지");
console.log("4. 에러 메시지 표시 후 return");
console.log("5. 예약 링크 생성되지 않음");
