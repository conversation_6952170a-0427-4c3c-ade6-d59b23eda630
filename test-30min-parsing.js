// 30분 파싱 테스트
const { parseMessage } = require('./dist/nlpHandler');

async function test30MinParsing() {
  const testMessages = [
    "오늘 포커스룸 동쪽 오후 2시부터 30분 예약해줘",
    "오늘 포커스룸 동쪽 오후 2시부터 삼십분 예약해줘",
    "포커스룸 동쪽 2시 30분 예약",
    "포커스룸 동쪽 2시 삼십분 예약"
  ];

  console.log("=== 30분 지속시간 파싱 테스트 ===\n");

  for (let i = 0; i < testMessages.length; i++) {
    const message = testMessages[i];
    console.log(`${i + 1}. "${message}"`);
    
    try {
      const result = await parseMessage(message);
      console.log("   파싱 결과:");
      console.log("   - duration:", result.duration);
      console.log("   - time:", result.time);
      console.log("   - room:", result.room);
      console.log("   - isReservationRequest:", result.isReservationRequest);
      
      if (result.duration === 30) {
        console.log("   ✅ 30분 지속시간 정상 파싱");
      } else {
        console.log(`   ❌ 예상: 30분, 실제: ${result.duration}분`);
      }
    } catch (error) {
      console.log("   ❌ 파싱 에러:", error.message);
    }
    
    console.log("");
  }
}

test30MinParsing();
