{"name": "reservation-bot", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "tsc", "start": "node dist/app.js", "dev": "ts-node-dev --respawn --transpile-only src/app.ts"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@huggingface/inference": "^3.13.2", "@slack/bolt": "^4.3.0", "@slack/web-api": "^7.9.2", "@types/open": "^6.1.0", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "google-auth-library": "^9.15.1", "googleapis": "^148.0.0", "open": "^10.1.2"}, "devDependencies": {"@types/node": "^22.15.18", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "eslint": "^9.26.0", "nodemon": "^3.1.10", "prettier": "^3.5.3", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}