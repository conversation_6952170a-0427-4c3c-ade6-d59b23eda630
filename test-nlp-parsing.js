// NLP 파싱 테스트
const { parseMessage } = require('./dist/nlpHandler');

async function testParsing() {
  const testMessage = "오늘 라이트룸 1시부터 3시간 예약해줘";
  
  console.log("=== NLP 파싱 테스트 ===");
  console.log(`입력 메시지: "${testMessage}"`);
  console.log("");
  
  try {
    const result = await parseMessage(testMessage);
    
    console.log("파싱 결과:");
    console.log("- date:", result.date);
    console.log("- time:", result.time);
    console.log("- room:", result.room);
    console.log("- duration:", result.duration);
    console.log("- isReservationRequest:", result.isReservationRequest);
    console.log("- isAvailabilityCheck:", result.isAvailabilityCheck);
    console.log("- needsRoomSelection:", result.needsRoomSelection);
    console.log("- attendeeCount:", result.attendeeCount);
    
    console.log("\n=== 바로 예약 확정 분기 조건 체크 ===");
    const shouldGoDirectReservation = 
      result.isReservationRequest &&
      result.date &&
      result.room &&
      result.time &&
      !result.needsRoomSelection;
    
    console.log("조건 체크:");
    console.log("- isReservationRequest:", result.isReservationRequest);
    console.log("- date 존재:", !!result.date);
    console.log("- room 존재:", !!result.room);
    console.log("- time 존재:", !!result.time);
    console.log("- needsRoomSelection:", result.needsRoomSelection);
    console.log("- 바로 예약 분기 진입:", shouldGoDirectReservation);
    
  } catch (error) {
    console.error("파싱 에러:", error);
  }
}

testParsing();
